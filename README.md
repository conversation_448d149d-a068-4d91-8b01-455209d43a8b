# Introduction

This is the repository to store all DevOps files for the Crimson marketing website.

Currently this repo includes:

- CGA-Website: Terraform templates to create Fastly distributions for the test, staging and production environments for crimsonglobalacademy.school
- Marketing-Website: Terraform templates to create Fastly distributions for the test, staging and production environments for crimsoneducation.org
- Marketing-API: Terraform templates to create marketing api infrastructure for staging and production environments for backend of crimsoneducation.org

For more specific information on the architecture of each of the DevOps file, refer to the README file in respective folders

## Prerequisites

- Make sure that Terraform is installed. On the Mac, run `brew install terraform`.
- `brew install tfenv`
- Use `TFENV_ARCH=amd64 tfenv install 0.13.7` if you are using an M1 Mac.
- Minimal Required Terraform version: 0.12

## Development Process:

1. Once your PR is approved, changes should be deployed to production, then merged as soon as possible.
2. All PRs should be reviewed and approved _before_ applying to production
3. Any open change requests should be inquired about before making production changes.

## Further Readings

- [Official Terraform Docs](https://www.terraform.io/docs/index.html)
