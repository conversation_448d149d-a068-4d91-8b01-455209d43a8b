{"name": "crimson-marketing-devops", "description": "Some scripts to deploy and test Terraform configurations.", "version": "1.0.1", "main": "index.js", "license": "MIT", "dependencies": {"request": "^2.88.0", "request-promise-native": "^1.0.5"}, "scripts": {"test": "jest --config jest.config.js --passWithNoTests --silent", "test-noisy": "jest --config jest.config.js --passWithNoTests", "test-watch": "jest  --config jest.config.js --watch", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "apply-default": "cd ./terraform/ && ./apply-default.sh && cd ..", "apply-staging": "cd ./terraform/ && ./apply-staging.sh && cd ..", "apply-prod": "cd ./terraform/ && ./apply-prod.sh && cd ..", "apply-and-test": "npm run apply-default && npm run test", "format": "prettier --write \"**/*.{js,jsx,json,md}\""}, "devDependencies": {"babel-eslint": "^10.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^4.3.0", "eslint-plugin-jest": "^23.8.2", "husky": "^2.7.0", "jest": "^25.2.0", "prettier": "^1.17.1", "pretty-quick": "^1.11.0"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged && npm run lint", "pre-push": "npm run test"}}}