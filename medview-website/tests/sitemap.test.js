// const { get } = require('./http');

describe('sitemap.xml', () => {
  it('should return true', () => {
    expect(true).toBe(true);
  });

  // TODO: To be discussed with the team. unsure if this is the correct url
  //   it('should return 200 for sitemap.xml without any redirection', async () => {
  //     const response = await get(
  //       // will change to medview url after the site is deployed
  //       'https://www.test.medvieweducation.org/sitemap.xml'
  //     );
  //     expect(response.statusCode).toBe(200);
  //   }, 30000);
  //   it('robots.txt should return 200 without redirection', async () => {
  //     const response = await get(
  //       'https://www.test.medvieweducation.org/robots.txt'
  //     );
  //     expect(response.statusCode).toBe(200);
  //   });
});
