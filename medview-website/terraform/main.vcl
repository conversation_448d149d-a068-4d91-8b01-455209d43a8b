include "Utils";
include "Redirects VCL";

sub vcl_recv {
  #FASTLY recv
  declare local var.locale STRING;
  declare local var.path STRING;
  declare local var.localeLessPath STRING;
  declare local var.sitemapName STRING;

  // Check we aren't serving US Embargoed countries.
  call check_blocklist;

  // Avoid using apex domains.
  if (req.http.host !~ "^www\." && req.http.host !~ "^staging\." && req.http.host !~ "^test\." && req.http.host ~ "medvieweducation\.org$") {
    error 701 "Force www.";
  }
 // 301 Redirect if the url is one of the old marketo pages in crimson website. No country code here.
 if (table.lookup(marketoRedirects,std.tolower(regsub(req.url.path, "/$", "")))) {
    error 704 "Marketo Redirect";
 }
  // 302 Redirect marketing campaign URLs. E.g. /FLBA
  if (table.lookup(campaignRedirects,std.tolower(regsub(req.url.path, "/$", "")))) {
    error 707 "Temporary Campaign Redirect";
 }

  // Geo block people from restricted country
  // Current blocked countries are Cuba, Iran, Syria, North Korea and Crimea (Region of Ukraine)
  if (client.geo.country_code == "CU" ||client.geo.country_code == "IR" || client.geo.country_code == "SY" ||client.geo.country_code == "KP" || client.geo.region == "UA-43") {
    error 405 "Not allowed";
  }

  // Skip static assets. Check for locales in rest of the requests.
  if (
    req.url !~ "^/(dist|images|klshokncel54efppfntg-images|static)/"
    && req.url !~ "404\.html"
    && req.url !~ "\.(ico|jpg|png|svg|js|map|css|woff|woff2|txt|xml|json|webmanifest)$"
    && req.url !~ "page-data\.json"
  ) {
    // redirect /abcd/ to /nz/abcd/
    // If the URL does not follow the pattern /aa or /aa/ or /aa-bb or /aa-bb/, then we assume there is no country code in the URL
  #  if (req.url !~ "(?i)^/(nz|au|global-content)(/|\?|$)") {
  #     error 710 "Inject geolocation into URL";
  #  }

    // If we reach this point, the URL has the format /${locale}/${path},  and we are good to respond to the request

    // Check if the locale is any of the deprecated ones for a 301 redirect.
    set var.locale = std.tolower(if(req.url ~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2})(/|\?|$)",re.group.1, ""));
    if(table.lookup(localeRedirects, var.locale)){
       error 713 "Permanently Moved Locales";
   }

    //Remove slash from the end of the url
    set var.path = std.tolower(regsub(req.url.path, "/$", ""));

    //Check if the URL need to redirected for specific locale
    if(table.lookup(localeSiteRedirects, var.path)){
      error 714 "Local site redirects";
    }

   if (req.url ~ "^/au(/|$)") {
      error 715 "AU to base redirect";
    }

    // Check if the path is one of the site 301 redirects.
    // All the paths in the redirect tables are expected to be lower case.
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
    set var.localeLessPath = regsub(var.localeLessPath,"/$","");
    if(table.lookup(siteRedirects,var.localeLessPath)){
       error 716 "Permanently Moved Paths";
    }
    //Check if the path needs to be redirected to an external site.
    # if(table.lookup(externalSiteRedirects,var.localeLessPath)){
    #     error 720 "external website redirect";
    # }
    set req.url = regsub(req.url, "\/diagnostics\/report\/\b[0-9a-f]{8}\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\b[0-9a-f]{12}\b$", "/diagnostics/report/");
    set req.url = regsub(req.url, "\/diagnostics\/diagnostics-report-pdf\/\b[0-9a-f]{8}\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\b[0-9a-f]{12}\b$", "/diagnostics/diagnostics-report-pdf/");
  }
  // Set the back end and the host here. Amazon S3 only accepts host header containing it's bucket address.
  // Match the backend name in fastly.
  set req.backend = F_medview;
  if (req.http.host ~ "test.") {
      set req.http.host = "medview-website-staging-preview.s3-website-ap-southeast-2.amazonaws.com";
  }else if(req.http.host ~ "staging."){
      set req.http.host = "medview-website-staging.s3-website-ap-southeast-2.amazonaws.com";
  }else {
      set req.http.host = "medview-production.s3-website-ap-southeast-2.amazonaws.com";
  }

   // Set a '/' at the end of the path if not present. This makes sure that s3 preserves the querystring params or else it is overridden.
   // This bug only happens when the fastly cache is not hit which is pretty rare.
   if(req.url.path !~ "(?i)/$" && req.url.path !~ "(?i)\.\w+$") {
      set req.url = req.url.path "/" if(req.url.qs != "", "?" req.url.qs, "");
   }

  if (req.request != "HEAD" && req.request != "GET" && req.request != "FASTLYPURGE") {
    return(pass);
  }

  return(lookup);
}

sub vcl_fetch {
  #FASTLY fetch
  if ((beresp.status == 500 || beresp.status == 503) && req.restarts < 1 && (req.request == "GET" || req.request == "HEAD")) {
    restart;
  }

  if (req.restarts > 0) {
    set beresp.http.Fastly-Restarts = req.restarts;
  }

  if (beresp.http.Set-Cookie) {
    set req.http.Fastly-Cachetype = "SETCOOKIE";
    return(pass);
  }

  if (beresp.http.Cache-Control ~ "private") {
    set req.http.Fastly-Cachetype = "PRIVATE";
    return(pass);
  }

  if (beresp.status == 500 || beresp.status == 503) {
    set req.http.Fastly-Cachetype = "ERROR";
    set beresp.ttl = 1s;
    set beresp.grace = 5s;
    return(deliver);
  }

  // Ignroing the Cache-Control max-age parameter here under the assumption that max-age is only for browser side.
  if (beresp.http.Expires || beresp.http.Surrogate-Control ~ "max-age" || beresp.http.Cache-Control ~ "(s-maxage)") {
    # keep the ttl here
  } else {
    // Cache time of the object - 24 hours.
    set beresp.ttl = 86400s;
  }
  if (beresp.status == 404) {
    error 722 "404 Custom Response";
  }

  return(deliver);
}

sub vcl_hit {
  #FASTLY hit
  if (!obj.cacheable) {
    return(pass);
  }
  return(deliver);
}

sub vcl_miss {
#FASTLY miss
  return(fetch);
}

sub vcl_deliver {
#FASTLY deliver
  return(deliver);
}

sub vcl_error {
  #FASTLY error

  declare local var.localeLessPath STRING;
  declare local var.locale STRING;
  declare local var.parentPath STRING;
  declare local var.hostPath STRING;
  // Handle naked domain error.
  if(obj.status == 701){
    set obj.status = 301;
    set obj.http.Location =  "https://" "www." req.http.host req.url;
    set obj.response = "No Naked Domains";
    synthetic {""};
    return (deliver);
  }

  // Handle marketo pages 301 redirection.
  if (obj.status == 704) {
    set obj.status = 301;
    set obj.http.Location = table.lookup(marketoRedirects, std.tolower(regsub(req.url.path, "/$", "")));
    set obj.response = "Marketo 301 Redirect";
    if(req.url.qs != "") {
      set obj.http.Location = obj.http.Location "?" req.url.qs ;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle marketing page 302 redirects for campaigns.
  if (obj.status == 707) {
    set obj.status = 302;
    set obj.http.Location = table.lookup(campaignRedirects, std.tolower(regsub(req.url.path, "/$", "")));
    if(req.url.qs != ""){
      if (obj.http.Location ~ "\?.*$") {
        set obj.http.Location = obj.http.Location "&" req.url.qs;
      } else {
        set obj.http.Location = obj.http.Location "?" req.url.qs;
      }
     }
    set obj.response = "Marketing Page Redirect";
    synthetic {""};
    return (deliver);
  }

  // Handle geo-redirection.
  if (obj.status == 710) {
    set obj.status = 302;
    set obj.response = "Geo Redirect";
    set var.locale = table.lookup(geoCountryRedirects,std.tolower(client.geo.country_code),"");
    // If the locale is none from the list we support then assume that the rest of the url needs to be redirected.
    if(!var.locale || var.locale == ""){
      # set var.locale = "au";
      set obj.http.Location = "https://" req.http.host  "/" req.url;
    } else {
      set obj.http.Location = "https://" req.http.host  "/" var.locale req.url;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle all the 301 redirects for deprecated locales.
  if (obj.status == 713) {
    set obj.status = 301;
    set obj.response = "Deprecated Locale Redirect";
    // Extract the locale out.
    set var.locale = if(req.url.path ~ "(?i)^/(([a-z]{2}-[a-z]{2}|[a-z]{2}))", re.group.1,"");
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)",""));
    set obj.http.Location =  "https://" req.http.host "/" table.lookup(localeRedirects, var.locale) var.localeLessPath;
    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }


  if(obj.status == 714){
    set obj.status = 301;
    set obj.response = "Locale specific site redirect";
    set obj.http.Location =  "https://" req.http.host table.lookup(localeSiteRedirects, std.tolower(regsub(req.url.path, "/$", "")));
    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }

  if (obj.status == 715) {
    set obj.status = 301;
    set obj.response = "AU to base redirect";
    // Remove the /au prefix, similar to other redirect handlers
    set var.localeLessPath = regsub(req.url.path, "^/au", "");
    if (var.localeLessPath == "") {
      set var.localeLessPath = "/";
    }
    set obj.http.Location = "https://" req.http.host var.localeLessPath;

    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }

    synthetic {""};
    return (deliver);
  }

  // Handle all the 301 redirects with in the crimson website.
  if (obj.status == 716) {
    set obj.status = 301;
    set obj.response = "Deprecated Page Redirect";
    // Extract the country code out and add again with the new path.
    set var.locale = if(req.url.path ~ "(?i)^/(([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content))/", re.group.1,"");
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
    set var.localeLessPath = regsub(var.localeLessPath,"/$","");
    set obj.http.Location =  "https://" req.http.host "/" var.locale table.lookup(siteRedirects, var.localeLessPath);
    if(req.url.qs != ""){
       set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }


  // Handle domains redirection 301 redirection.
  if (obj.status == 723) {
    set obj.status = 301;
    set obj.response = "301 Redirect";
    set var.hostPath = "https://www.medvieweducation.org";
    set obj.http.Location = "https://www.medvieweducation.org";
    synthetic {""};
    return (deliver);
  }

  // Redirect all 404 pages to known list.
  if (obj.status == 722) {
  // Since the host is already set to s3 bucket when 404 occurs reset it to the crimson education url path.
    if (req.http.host ~ "medview-website-staging-preview.s3-website-ap-southeast-2.amazonaws.com") {
      set var.hostPath = "https://test.medvieweducation.org";
      
    }
    else if(req.http.host ~ "medview-website-staging.s3-website-ap-southeast-2.amazonaws.com"){
      set var.hostPath = "https://staging.medvieweducation.org";
    }
    else {
     set var.hostPath = "https://www.medvieweducation.org";
    }
   // Extract the locale out and check if it is a valid one by comparing it with our valid locales list else default to nz.
   set var.locale = if(req.url.path ~ "(?i)^/(nz|global-content)(/|\?|$)", re.group.1,"");
   set var.locale =  table.lookup(localeList,var.locale,"");
   set var.parentPath = if(req.url.path ~ "(?i)^/(nz|global-content)(\/.+?)?(\/.*)?$", re.group.2,"");
   set var.localeLessPath =  table.lookup(parent404Redirects, var.parentPath,"");
   
   // If no valid parent path is found redirect to the home page.
    if(!var.localeLessPath || var.localeLessPath == ""){
      // For empty locale, don't add a locale prefix
      set obj.http.Location = var.hostPath if(var.locale != "", "/" var.locale "/", "/");
    } else {
      // For empty locale, don't add a locale prefix
      set obj.http.Location = var.hostPath if(var.locale != "", "/" var.locale, "") var.localeLessPath "/";
    }
    set obj.status = 302;
    set obj.response = "Redirecting 404";
    synthetic {""};
    return (deliver);
  }
}

sub vcl_pass {
#FASTLY pass
}

sub vcl_log {
#FASTLY log
}
