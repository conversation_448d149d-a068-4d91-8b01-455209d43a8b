const { get } = require('./http');

describe('sitemap.xml', () => {
  it('should return 200 for sitemap.xml without any redirection', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.org/sitemap.xml'
    );
    expect(response.statusCode).toBe(200);
  });

  it('robots.txt should return 200 without redirection', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.org/robots.txt'
    );
    expect(response.statusCode).toBe(200);
  });

  it('should return 200 for robots.txt for NZ domain without redirection', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.co.nz/robots.txt'
    );
    expect(response.statusCode).toBe(200);
  });

  it('should return 200 for sitemap.xml for NZ domain without redirection', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.org.nz/sitemap.xml'
    );
    expect(response.statusCode).toBe(200);
  });
});
