const { get } = require('./http');

describe('HTTPS Redirection', () => {
  it('should redirect HTTP to HTTPS', async () => {
    const response = await get('http://www.test.crimsoneducation.org');
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/'
    );
  });
});

describe('Naked domain redirection', () => {
  it('should redirect naked to www domain', async () => {
    const response = await get('https://test.crimsoneducation.org');
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/'
    );
  });
});

describe('Geolocation code should be injected', () => {
  it('should redirect / to /nz', async () => {
    const response = await get('https://www.test.crimsoneducation.org');
    expect(response.statusCode).toBe(302);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/nz/'
    );
  });

  it('should redirect /abcd to /nz/abcd', async () => {
    const response = await get('https://www.test.crimsoneducation.org/abcd');
    expect(response.statusCode).toBe(302);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/nz/abcd'
    );
  });

  it('should redirect /qs-crimson to /nz/qs-crimson', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.org/qs-crimson'
    );
    expect(response.statusCode).toBe(302);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/nz/qs-crimson'
    );
  });

  it('should not inject country code for /nz?x=123', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.org/nz?x=123'
    );
    expect(response.statusCode).toBe(200);
  });
});

describe('Locale specific redirection', () => {
  it('should redirect /us/medview to /nz/medview', async () => {
    const response = await get(
      'https://www.test.crimsoneducation.org/us/medview'
    );
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/nz/our-services/medview'
    );
  });
});

describe('Legacy redirection', () => {
  it('should redirect /gb to /uk', async () => {
    const response = await get('https://www.test.crimsoneducation.org/gb');
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsoneducation.org/uk'
    );
  });
});

describe('No redirections', () => {
  it('/nz should work without redirection', async () => {
    const response = await get('https://www.test.crimsoneducation.org/nz');
    expect(response.statusCode).toBe(200);
  });
});

describe('NZ domains redirection', () => {
  it('should redirect NZ domains to main domains', async () => {
    const responses = await Promise.all([
      get('https://www.test.crimsoneducation.co.nz/blog'),
      get('https://www.test.crimsoneducation.org.nz/blog'),
    ]);
    responses.forEach(response => {
      expect(response.statusCode).toBe(302);
      expect(response.headers['location']).toBe(
        'https://www.test.crimsoneducation.org/nz/blog'
      );
    });
  });
});
