const { get } = require('./http');

const randomString = (
  strLength = 4,
  charSet = 'abcdefghijklmnopqrstuvwxyz'
) => {
  const result = [];
  while (result.length < strLength) {
    result.push(charSet.charAt(Math.floor(Math.random() * charSet.length)));
  }
  return result.join('');
};

const getStaticFromBody = body => {
  const jsFiles = body.match(/(href|src)="\/\S+?\.(json|js)/gi);
  const cssFiles = body.match(/(href|src)="\/\S+?\.css/gi);
  return []
    .concat(jsFiles)
    .concat(cssFiles)
    .filter(route => route)
    .map(staticRoute => {
      return staticRoute.replace('href="', '').replace('src="', '');
    })
    .filter(route => !route.startsWith('//')); // ignore 3rd party files.
};

const getResponseCodes = (
  routes,
  host = 'https://www.test.crimsoneducation.org'
) => {
  return Promise.all(
    routes.map(async route => {
      const response = await get(`${host}${route}`);
      return response.statusCode;
    })
  );
};

module.exports = {
  randomString,
  getStaticFromBody,
  getResponseCodes,
};
