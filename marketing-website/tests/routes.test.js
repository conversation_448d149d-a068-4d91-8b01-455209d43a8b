const { get } = require('./http');
const {
  randomString,
  getStaticFromBody,
  getResponseCodes,
} = require('./utils');

describe('Check major website routes', () => {
  const s3Routes = ['/contact', '/resources/essay-review'];

  s3Routes.forEach(route => {
    it(`should get ${route} from AWS S3`, async () => {
      const response = await get(
        `https://www.test.crimsoneducation.org/nz${route}/`
      );
      expect(response.statusCode).toBe(200);
      expect(response.headers.server).toBe('AmazonS3');
    });
  });

  describe('Essay review tests', () => {
    const route = '/resources/essay-review';

    it(`should get ${route} with no trailing slash`, async () => {
      const response = await get(
        `https://www.test.crimsoneducation.org/nz${route}`,
        { followRedirect: true }
      );
      expect(response.statusCode).toBe(200);
      expect(response.headers.server).toBe('AmazonS3');
    });

    it(`should get ${route} with a query parameter`, async () => {
      const queryKey = randomString();
      const queryValue = randomString();
      const response = await get(
        `https://www.test.crimsoneducation.org/nz${route}/?${queryKey}=${queryValue}`,
        { followRedirect: true }
      );
      expect(response.statusCode).toBe(200);
      expect(response.headers.server).toBe('AmazonS3');
    });

    it(`should get ${route} with a query parameter entered incorrectly`, async () => {
      const queryKey = randomString();
      const queryValue = randomString();
      const response = await get(
        `https://www.test.crimsoneducation.org/nz${route}?${queryKey}=${queryValue}`,
        { followRedirect: true }
      );
      expect(response.statusCode).toBe(200);
      expect(response.headers.server).toBe('AmazonS3');
    });

    it(`should load static files properly from ${route}`, async () => {
      const response = await get(
        `https://www.test.crimsoneducation.org/nz${route}/`,
        { gzip: true }
      );
      const { body } = response;
      const staticFiles = getStaticFromBody(body);
      const responseCodes = await getResponseCodes(staticFiles);
      responseCodes.forEach(code => {
        expect(code).toBe(200);
      });
    });
  });
});

describe('Check long tail blog generator routes', () => {
  const s3Domain =
    'http://crimson-website-v4-staging-preview.s3-website-ap-southeast-2.amazonaws.com';
  const fastlyDomain = 'https://www.test.crimsoneducation.org';
  const commonPath =
    '/global-content/resources/universities/uk-universities-that-accept/AAB/Arts-&-Humanities:-Architecture/';

  it("should get 404 from AWS S3 because it doesn't exist", async () => {
    const response = await get(`${s3Domain}${commonPath}`);
    expect(response.statusCode).toBe(404);
  });

  it('should get 200 from Fastly', async () => {
    const response = await get(`${fastlyDomain}${commonPath}`);
    expect(response.statusCode).toBe(200);
  });
});
