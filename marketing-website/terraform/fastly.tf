
provider "fastly" {
  api_key = var.FASTLY_API_KEY
}

locals {
  env = terraform.workspace

  names = {
    adrian  = "Crimson Marketing (adrian)"
    default = "Crimson Marketing (test)"
    staging = "Crimson Marketing (staging)"
    prod    = "Crimson Marketing (prod)"
  }

  ce_backends = {
    default = "websites-hosting-ce-staging-preview.s3-website-ap-southeast-2.amazonaws.com"
    staging = "websites-hosting-ce-staging.s3-website-ap-southeast-2.amazonaws.com"
    prod    = "websites-hosting-ce-production.s3-website-ap-southeast-2.amazonaws.com"
  }

  vercel_backends = {
    default = "staging-crimson-websites-next.vercel.app"
    staging = "staging-crimson-websites-next.vercel.app"
    prod    = "crimson-websites-next.vercel.app"
  }

  naked_domains = {
    adrian  = "test2.crimsoneducation.org"
    default = "test.crimsoneducation.org"
    staging = "staging.crimsoneducation.org"
    prod    = "crimsoneducation.org"
  }

  www_domains = {
    adrian  = "www.test2.crimsoneducation.org"
    default = "www.test.crimsoneducation.org"
    staging = "www.staging.crimsoneducation.org"
    prod    = "www.crimsoneducation.org"
  }
  // IMPROVEMENT: Restructure into dynamic
  community_domains = {
    default = "test.community.crimsoneducation.org.nz"
    staging = "staging.community.crimsoneducation.org.nz"
    prod    = "community.crimsoneducation.org"
  }
  design_domains = {
    default = "test.design.crimsoneducation.org.nz"
    staging = "staging.design.crimsoneducation.org.nz"
    prod    = "design.crimsoneducation.org"
  }
  events_domains = {
    default = "test.events.crimsoneducation.org.nz"
    staging = "staging.events.crimsoneducation.org.nz"
    prod    = "events.crimsoneducation.org"
  }

  tenYears_domains = {
    default = "test.10years.crimsoneducation.org"
    staging = "staging.10years.crimsoneducation.org"
    prod    = "10years.crimsoneducation.org"
  }

  learn_domains = {
    default = "test.learn.crimsoneducation.org.nz"
    staging = "staging.learn.crimsoneducation.org.nz"
    prod    = "learn.crimsoneducation.org"
  }

  redirect_domains_org_nz_naked = {
    adrian  = "test.crimsoneducation.org.nz"
    default = "test.crimsoneducation.org.nz"
    staging = "staging.crimsoneducation.org.nz"
    prod    = "crimsoneducation.org.nz"
  }

  redirect_domains_org_nz_www = {
    adrian  = "www.test.crimsoneducation.org.nz"
    default = "www.test.crimsoneducation.org.nz"
    staging = "www.staging.crimsoneducation.org.nz"
    prod    = "www.crimsoneducation.org.nz"
  }

  redirect_domains_co_nz_naked = {
    adrian  = "test.crimsoneducation.co.nz"
    default = "test.crimsoneducation.co.nz"
    staging = "staging.crimsoneducation.co.nz"
    prod    = "crimsoneducation.co.nz"
  }

  redirect_domains_co_nz_www = {
    adrian  = "www.test.crimsoneducation.co.nz"
    default = "www.test.crimsoneducation.co.nz"
    staging = "www.staging.crimsoneducation.co.nz"
    prod    = "www.crimsoneducation.co.nz"
  }

  name                         = local.names[local.env]
  ce_backend                   = local.ce_backends[local.env]
  vercel_backend               = local.vercel_backends[local.env]
  naked_domain                 = local.naked_domains[local.env]
  www_domain                   = local.www_domains[local.env]
  community_domain             = local.community_domains[local.env]
  design_domain                = local.design_domains[local.env]
  events_domain                = local.events_domains[local.env]
  learn_domain                 = local.learn_domains[local.env]
  redirect_domain_co_nz_naked  = local.redirect_domains_co_nz_naked[local.env]
  redirect_domain_co_nz_www    = local.redirect_domains_co_nz_www[local.env]
  redirect_domain_org_nz_naked = local.redirect_domains_org_nz_naked[local.env]
  redirect_domain_org_nz_www   = local.redirect_domains_org_nz_www[local.env]
  tenYears_domain              = local.tenYears_domains[local.env]

  # Datadog
  # See: https://docs.fastly.com/en/guides/custom-log-formats for log formats
  dd_format_object = {
    "service" : "fastly-core",
    "env" : local.env,
    "userAgent" : "%%{User-agent}i",
    "referer" : "%%{Referer}i",
    "message" : "%m %U",
    "time" : "%t",
    "remoteIP" : "%a",
    "host" : "%V",
    "request" : "%U",
    "query" : "%q",
    "method" : "%m",
    "status" : "%s"
  }
  # We need the `replace` here because otherwise `jsonencode` would also encodes the `>` character
  dd_format = replace(jsonencode(local.dd_format_object), "%s", "%>s")
}

# Create a new Fastly service called crimsoneducation_org
# Fastly_service_v1 docs: https://www.terraform.io/docs/providers/fastly/r/service_v1.html
resource "fastly_service_v1" "crimsoneducation_org" {
  name = local.name

  # logging_datadog {
  #   name   = "logging-datadog-${local.env}"
  #   token  = var.DD_API_KEY
  #   format = local.dd_format
  #   region = "EU"
  # }

  domain {
    name = local.naked_domain
  }

  domain {
    name = local.www_domain
  }

  domain {
    name = local.community_domain
  }

  domain {
    name = local.design_domain
  }

  domain {
    name = local.events_domain
  }

  domain {
    name = local.learn_domain
  }

  domain {
    name = local.tenYears_domain
  }

  domain {
    name = local.redirect_domain_org_nz_naked
  }

  domain {
    name = local.redirect_domain_co_nz_naked
  }

  domain {
    name = local.redirect_domain_org_nz_www
  }

  domain {
    name = local.redirect_domain_co_nz_www
  }

  backend {
    address          = "crimson-echo.herokuapp.com"
    name             = "echo"
    port             = 443
    use_ssl          = true
    ssl_check_cert   = false
    auto_loadbalance = false
    shield           = "sydney-au"
  }

  backend {
    address          = local.ce_backend
    name             = "ce"
    port             = 80
    use_ssl          = false
    ssl_check_cert   = false
    auto_loadbalance = false
    override_host    = local.ce_backend
    shield           = "sydney-au"
  }

  backend {
    address          = local.vercel_backend
    name             = "vercel"
    port             = 443
    use_ssl          = true
    ssl_check_cert   = false
    auto_loadbalance = false
    shield           = "bfi-wa-us"
  }

  force_destroy = true

  request_setting {
    name      = "Force SSL"
    force_ssl = true
  }

  gzip {
    name          = "Default Gzip Policy"
    content_types = ["text/html", "application/x-javascript", "text/css", "application/javascript", "text/javascript", "application/json", "application/vnd.ms-fontobject", "application/x-font-opentype", "application/x-font-truetype", "application/x-font-ttf", "application/xml", "font/eot", "font/opentype", "font/otf", "image/svg+xml", "image/vnd.microsoft.icon", "text/plain", "text/xml"]
    extensions    = ["css", "js", "html", "eot", "ico", "otf", "ttf", "json", "svg"]
  }

  vcl {
    name    = "Main VCL file"
    content = file("${path.module}/main.vcl")
    main    = true
  }

  vcl {
    name    = "Utils"
    content = file("${path.module}/../../shared/utils.vcl")
  }

  vcl {
    name    = "Redirects VCL"
    content = file("${path.module}/redirects.vcl")
  }

}
