include "Utils";
include "Redirects VCL";

sub vcl_recv {
#FASTLY recv
  declare local var.locale STRING;
  declare local var.path STRING;
  declare local var.localeLessPath STRING;
  declare local var.sitemapName STRING;

  // Check we aren't serving US Embargoed countries.
  call check_blocklist;

  // 301 Redirect is the url is under community, design, events or learn
  if(req.http.host ~ "design\.crimsoneducation\.org$" || req.http.host ~ "community\.crimsoneducation\.org$" || req.http.host ~ "events\.crimsoneducation\.org$" || req.http.host ~ "learn\.crimsoneducation\.org$" || req.http.host ~ "10years\.crimsoneducation\.org$") {
     error 723 "Force Redirect 301";
  }

  // Don't use naked domains.
  if (req.http.host !~ "^www\." && req.http.host !~ "^staging\." && req.http.host !~ "^test\." && req.http.host ~ "crimsoneducation\.org$") {
    error 701 "Force www.";
  }

  // Rewrite sitemap request, so Google can have sitemap setup.
  if(req.url.path == "/sitemap.xml" && (req.http.host ~ "crimsoneducation\.org\.nz$" || req.http.host ~ "crimsoneducation\.co\.nz$")){
    set var.sitemapName = if(req.http.host ~ "(\.[^.]*\.[^.]*)$", re.group.1, "") ;
    set req.http.host = regsub(req.http.host, "(\.[^.]+){2}$", ".org"); 
    set req.url = regsub(req.url, "/sitemap.xml", "/sitemap_crimson") std.replaceall(var.sitemapName,".","_") ".xml";
  }

  if(req.url.path == "/robots.txt" && (req.http.host ~ "crimsoneducation\.org\.nz$" || req.http.host ~ "crimsoneducation\.co\.nz$")){
    error 709 "NZ Domain robots";
  }
  // Redirect nz domains for SEO
  if(req.http.host ~ "crimsoneducation\.org\.nz$" || req.http.host ~ "crimsoneducation\.co\.nz$"){
    error 702 "NZ Domain redirect";
  }

 // 301 Redirect if the url is one of the old marketo pages in crimson website. No country code here.
 if (table.lookup(marketoRedirects,std.tolower(regsub(req.url.path, "/$", "")))) {
    error 704 "Marketo Redirect";
 }

  // 302 Redirect marketing campaign URLs. E.g. /FLBA
 if (table.lookup(campaignRedirects,std.tolower(regsub(req.url.path, "/$", "")))) {
    error 707 "Temporary Campaign Redirect";
 }

  // Determine which backend and hostname to use
  if ((req.url.path ~ "^/([a-z]{2}(-[a-z]{2})?)/events(/[^/]+/?)?$" && !(req.url.path ~ "^/en/events")) || req.url.path ~ "^/_next/" || req.url.path ~ "^/api/") {
      // Check if the locale is any of the deprecated ones for a 301 redirect.
      set var.locale = std.tolower(if(req.url ~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2})(/|\?|$)",re.group.1, ""));
      if(table.lookup(localeRedirects, var.locale)){
        error 713 "Permanently Moved Locales";
      }

      //Remove slash from the end of the url
      set var.path = std.tolower(regsub(req.url.path, "/$", ""));

      //Check if the URL need to redirected for specific locale
      if(table.lookup(localeSiteRedirects, var.path)){
        error 714 "Local site redirects";
      }
      
      # Route to the Vercel backend for events landing and direct subpages only
      set req.backend = F_vercel;
      if(req.http.host ~ "test." || req.http.host ~ "staging."){
        set req.http.host = "staging-crimson-websites-next.vercel.app";
      } else {
        set req.http.host = "crimson-websites-next.vercel.app";
      }
  } else if (req.url ~ "^/echo") {
    // A simple server which echos back the request. Used for debugging.
    set req.backend = F_echo;
    set req.http.host = "crimson-echo.herokuapp.com";
  } else {
    // Skip static assets. Check for locales in rest of the requests.
    if (
      req.url !~ "^/(dist|images|klshokncel54efppfntg-images|static)/"
      && req.url !~ "404\.html"
      && req.url !~ "\.(ico|jpg|webp|avif|png|svg|js|map|css|woff|woff2|txt|xml|xsl|json|webmanifest)$"
      && req.url !~ "page-data\.json"
    ) {
      // redirect /abcd/ to /nz/abcd/
      // If the URL does not follow the pattern /aa or /aa/ or /aa-bb or /aa-bb/, then we assume there is no country code in the URL
      if (req.url !~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content|auth|visual-composer)(/|\?|$)") {
        error 710 "Inject geolocation into URL";
      }

      // If we reach this point, the URL has the format /${locale}/${path},  and we are good to respond to the request

      // Check if the locale is any of the deprecated ones for a 301 redirect.
      set var.locale = std.tolower(if(req.url ~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2})(/|\?|$)",re.group.1, ""));
      if(table.lookup(localeRedirects, var.locale)){
        error 713 "Permanently Moved Locales";
      }

      //Remove slash from the end of the url
      set var.path = std.tolower(regsub(req.url.path, "/$", ""));

      //Check if the URL need to redirected for specific locale
      if(table.lookup(localeSiteRedirects, var.path)){
        error 714 "Local site redirects";
      }

      // Check for MX blog redirects
      if (req.url ~ "^/mx/blog/") {
        error 725 "MX Blog Redirect";
      }

      // Check if the path is one of the site 301 redirects.
      // All the paths in the redirect tables are expected to be lower case.
      set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
      set var.localeLessPath = regsub(var.localeLessPath,"/$","");
      if(table.lookup(siteRedirects,var.localeLessPath)){
         error 716 "Permanently Moved Paths";
      }

      if (var.localeLessPath ~ "^/blog/.*/.+$"
          && var.localeLessPath !~ "^/blog/category/"
          && var.localeLessPath !~ "^/blog/authors/" ) {
        error 719 "Permanently Moved Blog Category Folders";
      }

      //Check if the path needs to be redirected to an external site.
      if(table.lookup(externalSiteRedirects,var.localeLessPath)){
          error 720 "external website redirect";
      }

      // WEB-2335: Check if the entire directory needs to be redirected to an external site.
      if(var.localeLessPath ~ "^/(gsa|our-services/gsa-campaigns|our-services/postgraduate-services)/"){
          error 724 "directory based external website redirect";
      }

      // Long tail blog generator is the first time we try with Gatsby client only routes.
      // We configure in Fastly as so that AWS S3 won't receive any request to pages that acually don't exist.
      // If no match is found, no replacement is made. (https://docs.fastly.com/vcl/functions/regsub/)
      set req.url = regsub(req.url, "/resources/universities/uk-universities-that-accept/.*$", "/resources/universities/uk-universities-that-accept/");
      set req.url = regsub(req.url, "/resources/universities/the-best-universities-to-study/.*$", "/resources/universities/the-best-universities-to-study/");
      set req.url = regsub(req.url, "/resources/universities/us-colleges-that-accept/.*$", "/resources/universities/us-colleges-that-accept/");

      // Diagnostic report need to accept path parameters from the url. AWS S3 won't receive any request to pages that are dynamic
      set req.url = regsub(req.url, "/cga/diagnostics-report/.*$", "/cga/diagnostics-report/");

      // We don't want users to be able to access the ask crimson page directly, so we redirect them to the blog page
      set req.url = regsub(req.url, "/resources/ask-crimson/.*$", "/blog");
    }

    // New pipeline to break the build into chunks and put into separate folders in the new bucket
    set req.backend = F_ce;
    if (req.url ~ "/ask-crimson" || req.url ~ "ask-crimson/") { // Ask Crimson build
      // Handle Gatsby asset prefix
      set req.url = regsub(req.url, "/assets-ask-crimson", "");
      set req.url = "/ask-crimson" req.url;
    } else if (req.url ~ "^/assets-build-chunk-1" // Build chunk 1
               || req.url ~ "^/global-content" // For staging-preview only (i.e. test.crimsoneducation.org)
               || req.url ~ "^/visual-composer"
               || req.url ~ "^/static/"
               || req.url ~ "^/ae"
               || req.url ~ "^/ae-en"
               || req.url ~ "^/au"
               || req.url ~ "^/az"
               # || req.url ~ "^/az-az"
               # || req.url ~ "^/az-ru"
               || req.url ~ "^/br"
               || req.url ~ "^/ca"
               # || req.url ~ "^/ca-zh"
               ) {
      set req.url = regsub(req.url, "/assets-build-chunk-1", "");
      set req.url = "/build-chunk-1" req.url;
    } else if (req.url ~ "^/assets-build-chunk-2" // Build chunk 2
               || req.url ~ "^/zh"
               || req.url ~ "^/cn-en"
               || req.url ~ "^/de"
               || req.url ~ "^/ge"
               || req.url ~ "^/hk"
               # || req.url ~ "^/hk-en"
               # || req.url ~ "^/hk-zh"
               || req.url ~ "^/in"
               ) {
      set req.url = regsub(req.url, "/assets-build-chunk-2", "");
      set req.url = "/build-chunk-2" req.url;
    } else if (req.url ~ "^/assets-build-chunk-3" // Build chunk 3
               || req.url ~ "^/id"
               # || req.url ~ "^/id-en"
               || req.url ~ "^/ja"
              #  || req.url ~ "^/jp-en"
               || req.url ~ "^/kh-en"
               || req.url ~ "^/kr"
               # || req.url ~ "^/kr-en"
               ) {
      set req.url = regsub(req.url, "/assets-build-chunk-3", "");
      set req.url = "/build-chunk-3" req.url;
    } else if (req.url ~ "^/assets-build-chunk-4" // Build chunk 4
               || req.url ~ "^/kz"
               # || req.url ~ "^/kz-en"
               # || req.url ~ "^/kz-ru"
               || req.url ~ "^/es"
               || req.url ~ "^/mm-en"
               || req.url ~ "^/mx"
               || req.url ~ "^/ap"
               # || req.url ~ "^/mx-en"
              #  || req.url ~ "^/ph-en"
              #  || req.url ~ "^/ee-en"
               || req.url ~ "^/ru"
               ) {
      set req.url = regsub(req.url, "/assets-build-chunk-4", "");
      set req.url = "/build-chunk-4" req.url;
    } else if (req.url ~ "^/assets-build-chunk-5" // Build chunk 5
               || req.url ~ "^/nz"
               || req.url ~ "^/th"
               # || req.url ~ "^/th-en"
               || req.url ~ "^/tr"
               # || req.url ~ "^/tr-en"
               || req.url ~ "^/tw"
               # || req.url ~ "^/tw-en"
               ) {
      set req.url = regsub(req.url, "/assets-build-chunk-5", "");
      set req.url = "/build-chunk-5" req.url;
    } else if (req.url ~ "^/assets-build-chunk-6" // Build chunk 6
               || req.url ~ "^/sg"
               # || req.url ~ "^/sg-zh"
               || req.url ~ "^/uk"
               || req.url ~ "^/us"
               # || req.url ~ "^/us-zh"
               || req.url ~ "^/vn"
               # || req.url ~ "^/vn-en"
               || req.url ~ "^/za"
               ) {
      set req.url = regsub(req.url, "/assets-build-chunk-6", "");
      set req.url = "/build-chunk-6" req.url;
    } else if (req.url ~ "^/sitemap" // Sitemaps
               || req.url ~ "^/baidusitemap"
               ) {
      set req.url = "/sitemaps" req.url;
    } else if (req.url ~ "^/robots\.txt$") { // robots.txt
      set req.url = "/robots_txt" req.url;
    } else {
      // no-op
    }
    // Set a '/' at the end of the path if not present. This makes sure that s3 preserves the querystring params or else it is overridden.
    // This bug only happens when the fastly cache is not hit which is pretty rare.
    if(req.url.path !~ "(?i)/$" && req.url.path !~ "(?i)\.\w+$") {
      set req.url = req.url.path "/" if(req.url.qs != "", "?" req.url.qs, "");
    }
  }

  if (req.request != "HEAD" && req.request != "GET" && req.request != "FASTLYPURGE") {
    return(pass);
  }

  return(lookup);
}

sub vcl_fetch {
#FASTLY fetch

  if ((beresp.status == 500 || beresp.status == 503) && req.restarts < 1 && (req.request == "GET" || req.request == "HEAD")) {
    restart;
  }

  if (req.restarts > 0) {
    set beresp.http.Fastly-Restarts = req.restarts;
  }

  if (beresp.http.Set-Cookie) {
    set req.http.Fastly-Cachetype = "SETCOOKIE";
    return(pass);
  }

  if (beresp.http.Cache-Control ~ "private") {
    set req.http.Fastly-Cachetype = "PRIVATE";
    return(pass);
  }

  if (beresp.status == 500 || beresp.status == 503) {
    set req.http.Fastly-Cachetype = "ERROR";
    set beresp.ttl = 1s;
    set beresp.grace = 5s;
    return(deliver);
  }

  // Ignroing the Cache-Control max-age parameter here under the assumption that max-age is only for browser side.
  if (beresp.http.Expires || beresp.http.Surrogate-Control ~ "max-age" || beresp.http.Cache-Control ~ "(s-maxage)") {
    # keep the ttl here
  } else {
    // Cache time of the object - 24 hours.
    set beresp.ttl = 86400s;
  }
  # if (beresp.status == 404) {
  #   error 722 "404 Custom Response";
  # }

  return(deliver);
}

sub vcl_hit {
#FASTLY hit

  if (!obj.cacheable) {
    return(pass);
  }
  return(deliver);
}

sub vcl_miss {
#FASTLY miss
  return(fetch);
}

sub vcl_deliver {
#FASTLY deliver
  return(deliver);
}

sub vcl_error {
#FASTLY error

  declare local var.localeLessPath STRING;
  declare local var.locale STRING;
  declare local var.parentPath STRING;
  declare local var.hostPath STRING;
  // Handle naked domain error.
  if(obj.status == 701){
    set obj.status = 301;
    set obj.http.Location =  "https://" "www." req.http.host req.url;
    set obj.response = "No Naked Domains";
    synthetic {""};
    return (deliver);
  }

  // Handle .org.nz and .co.nz domain redirect
  if(obj.status == 702){
    set obj.status = 302;
    # If url doesn't contain locale, assume it to be nz locale, otherwise, remove it.
    set var.locale = if(req.url !~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)(/|\?|$)","nz","");
    set var.localeLessPath = regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","");
    // keep whatever comes before the tld and replace the tld with .org
    // append nz locale and request url path to the end.
    // e.g. test.crimsoneducation.org.nz/blog => test.crimsoneducation.org/nz/blog
    set obj.http.Location = "https://" if(req.http.host !~ "^www\.", "www.","") regsub(req.http.host, "(\.[^.]+){2}$", ".org/") var.locale var.localeLessPath;
    set obj.response = "NZ domain redirects";
    synthetic {""};
    return (deliver);
  }

  // Handle marketo pages 301 redirection.
  if (obj.status == 704) {
    set obj.status = 301;
    set obj.http.Location = table.lookup(marketoRedirects, std.tolower(regsub(req.url.path, "/$", "")));
    set obj.response = "Marketo 301 Redirect";
    if(req.url.qs != "") {
      set obj.http.Location = obj.http.Location "?" req.url.qs ;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle marketing page 302 redirects for campaigns.
  if (obj.status == 707) {
    set obj.status = 302;
    set obj.http.Location = table.lookup(campaignRedirects, std.tolower(regsub(req.url.path, "/$", "")));
    if(req.url.qs != ""){
      if (obj.http.Location ~ "\?.*$") {
        set obj.http.Location = obj.http.Location "&" req.url.qs;
      } else {
        set obj.http.Location = obj.http.Location "?" req.url.qs;
      }
     }
    set obj.response = "Marketing Page Redirect";
    synthetic {""};
    return (deliver);
  }
  // Handle robots.txt for .co.nz and .org.nz domains
  if (obj.status == 709) {
    set obj.status = 200;
    set obj.response = "OK";
    set obj.http.Content-Type = "text/plain; charset=utf8";
    if(req.http.host ~ "test." || req.http.host ~ "staging."){
      synthetic "User-agent: *" LF "Disallow: /" LF
        "Sitemap: https://www.crimsoneducation" if(req.http.host ~ "(\.[^.]+){2}$", re.group.0, "") "/sitemap.xml"LF
        "Host: https://www.crimsoneducation" if(req.http.host ~ "(\.[^.]+){2}$", re.group.0, "");
    }
    else{
      synthetic "User-agent: *" LF "Allow: /" LF
        "Sitemap: https://www.crimsoneducation" if(req.http.host ~ "(\.[^.]+){2}$", re.group.0, "") "/sitemap.xml"LF
        "Host: https://www.crimsoneducation" if(req.http.host ~ "(\.[^.]+){2}$", re.group.0, "");
    }

    return (deliver);
  }

  // Handle geo-redirection.
  if (obj.status == 710) {
    set obj.status = 301;
    set obj.response = "Geo Redirect";
    set var.locale = table.lookup(geoCountryRedirects,std.tolower(client.geo.country_code),"");
    // If the locale is none from the list we support then assume that the rest of the url needs to be redirected.
    if(!var.locale || var.locale == ""){
       set var.locale = if (std.tolower(client.geo.continent_code) == "eu","uk","nz");
    }
    set obj.http.Location = "https://" req.http.host  "/" var.locale req.url;
    synthetic {""};
    return (deliver);
  }

  // Handle all the 301 redirects for deprecated locales.
  if (obj.status == 713) {
    set obj.status = 301;
    set obj.response = "Deprecated Locale Redirect";
    // Extract the locale out.
    set var.locale = if(req.url.path ~ "(?i)^/(([a-z]{2}-[a-z]{2}|[a-z]{2}))", re.group.1,"");
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)",""));
    
    declare local var.newLocale STRING;
    set var.newLocale = table.lookup(localeRedirects, var.locale);
    
    declare local var.newFullPath STRING;
    set var.newFullPath = "/" var.newLocale var.localeLessPath;
    set var.newFullPath = std.tolower(regsub(var.newFullPath, "/$", ""));
    
    if(table.lookup(localeSiteRedirects, var.newFullPath)) {
      set obj.http.Location = "https://" req.http.host table.lookup(localeSiteRedirects, var.newFullPath);
    } else {
      set obj.http.Location = "https://" req.http.host "/" var.newLocale var.localeLessPath;
    }
    
    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }


  if(obj.status == 714){
    set obj.status = 301;
    set obj.response = "Locale specific site redirect";
    set obj.http.Location =  "https://" req.http.host table.lookup(localeSiteRedirects, std.tolower(regsub(req.url.path, "/$", "")));
    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle all the 301 redirects with in the crimson website.
  if (obj.status == 716) {
    set obj.status = 301;
    set obj.response = "Deprecated Page Redirect";
    // Extract the country code out and add again with the new path.
    set var.locale = if(req.url.path ~ "(?i)^/(([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content))/", re.group.1,"");
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
    set var.localeLessPath = regsub(var.localeLessPath,"/$","");
    set obj.http.Location =  "https://" req.http.host "/" var.locale table.lookup(siteRedirects, var.localeLessPath);
    if(req.url.qs != ""){
       set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle 301 redirects for blog category folders
  if (obj.status == 719) {
    set obj.status = 301;
    set obj.response = "Blog Category Folder Redirect";
    set var.locale = if(req.url.path ~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/", re.group.1, "");
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
    set var.localeLessPath = regsub(var.localeLessPath,"/$","");
    // Redirect `/blog/folder/of/any/depth/article` to `/blog/article`
    set var.localeLessPath = regsub(var.localeLessPath, "^/blog/.*/(.+)$", "/blog/\1");
    set obj.http.Location =  "https://" req.http.host "/" var.locale var.localeLessPath "/";
    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle 301 redirects to all the external site redirects
  if (obj.status == 720) {
      set obj.status = 301;
      set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
      set var.localeLessPath = regsub(var.localeLessPath,"/$","");
      set obj.http.Location = table.lookup(externalSiteRedirects, var.localeLessPath);
      if(req.url.qs != ""){
          set obj.http.Location = obj.http.Location "?" req.url.qs;
       }
      set obj.response = "External Page Redirect";
      synthetic {""};
      return (deliver);
  }

  // Handle MX blog redirects to ES blog
  if (obj.status == 725) {
    set obj.status = 301;
    set obj.response = "MX Blog Redirect";
    set var.localeLessPath = regsub(req.url.path, "^/mx/blog/", "/es/blog/");
    set obj.http.Location = "https://" req.http.host var.localeLessPath;
    if(req.url.qs != "") {
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }
    synthetic {""};
    return (deliver);
  }

  // Handle broken domains 301 redirection.
  if (obj.status == 723) {
    set obj.status = 301;
    set obj.response = "Broken domains 301 Redirect";
    set var.hostPath = "https://www.crimsoneducation.org";
    set obj.http.Location = "https://www.crimsoneducation.org";
    synthetic {""};
    return (deliver);
  }

 // Redirect all 404 pages to known list.
  if (obj.status == 722) {
   // Extract the locale out and check if it is a valid one by comparing it with our valid locales list else default to nz.
   set var.locale = if(req.url.path ~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)(/|\?|$)", re.group.1,"");
   set var.locale =  table.lookup(localeList,var.locale,"nz");
   set var.parentPath = if(req.url.path ~ "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)(\/.+?)?(\/.*)?$", re.group.2,"");
   set var.localeLessPath =  table.lookup(parent404Redirects, var.parentPath,"");
   // If no valid parent path is found redirect to the home page.
    if(!var.localeLessPath || var.localeLessPath == ""){
      set obj.http.Location =  var.hostPath "/" var.locale "/";
    }
    else{
      set obj.http.Location = var.hostPath "/" var.locale var.localeLessPath "/";
    }
    set obj.status = 302;
    set obj.response = "Redirecting 404";
    synthetic {""};
    return (deliver);
  }

  # WEB-2335 
  if (obj.status == 724) {
    set obj.status = 301;
    set var.localeLessPath = std.tolower(regsub(req.url.path, "(?i)^/([a-z]{2}-[a-z]{2}|[a-z]{2}|global-content)/","/"));
    set var.localeLessPath = regsub(var.localeLessPath,"/$","");

    # Check for the matching pattern and set the location accordingly
    if (var.localeLessPath ~ "^/gsa/") {
      set obj.http.Location = table.lookup(externalDirectoryRedirects, "/gsa/");
    } 
    else if (var.localeLessPath ~ "^/our-services/gsa-campaigns/") {
      set obj.http.Location = table.lookup(externalDirectoryRedirects, "/our-services/gsa-campaigns/");
    } 
    else if (var.localeLessPath ~ "^/our-services/postgraduate-services/") {
      set obj.http.Location = table.lookup(externalDirectoryRedirects, "/our-services/postgraduate-services/");
    }

    if(req.url.qs != ""){
      set obj.http.Location = obj.http.Location "?" req.url.qs;
    }

    set obj.response = "External Page Redirect";
    synthetic {""};
    return (deliver);
  }
}

sub vcl_pass {
#FASTLY pass
}

sub vcl_log {
#FASTLY log
}
