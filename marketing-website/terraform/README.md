# Introduction

This is the repository to store all DevOps files for the Crimson marketing website. At the moment, this includes the Terraform templates to create Fastly distributions for the test, staging and production environments.

## Instructions

- Make sure that Terraform is installed. On the Mac, run `brew install terraform`.
- cd /terraform
- Make sure that the `.env` file inside the terraform folder is populated with the values provided in `.env.example`.
- Make any changes that you want to the Fastly distribution. There are 2 main files:
  - fastly.tf: This is main Terraform template. It describe all the properties of the 3 Fastly distributions (test, staging, production).
  - main.vcl: This is the VCL file for the Fastly distribution. We are only custom VCL file, and Fastly only support 1 of those, so we are including all the routing logic in that one file.

## Workspaces

- Workspace are Terraform's implementation of what we refer to as "environments".
- There are 3 different workspace:
  - default
  - staging
  - prod
- These workspace maps to the following Fastly distributions:
  - test.crimsoneducation.org (default)
  - staging.crimsoneducation.org (staging)
  - crimsoneducation.org (prod)
- To switch to a different workspace, run `terraform workspace select <workspace-name>`

## Deploying
Firstly, `cd terraform`.

To preview what changes you will be making to <PERSON>ly, run `source .env`, then run `terraform plan`.

To apply those changes to <PERSON>ly, run one of the three scripts based on the workspace where you want to deploy the changes.

- `./apply-default.sh`.
- `./apply-staging.sh`.
- `./apply-prod.sh`.

## Testing

We use jest to run all our unit tests.
To apply to test workspace and run all unit tests use command `npm run apply-and-test`

## What's left to do?

- To use Terraform effectively within a team, we will need to set up "remote state". In the current state of implementation, we are using local states, which means that all the states are stored locally inside the directory "terrform.tfstate.d". That is fine if there is a single person using Terraform. We will need to move to remote state at some point.
