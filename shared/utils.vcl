# https://developer.fastly.com/reference/vcl/subroutines/

// Call this in vcl_recv to ensure we aren't serving US Embargoed countries.
sub check_blocklist {
  // Geo block people from restricted country
  if (
      client.geo.country_code == "CU" // Cuba
      || client.geo.country_code == "IR" // Iran
      || client.geo.country_code == "SY" // Syria
      || client.geo.country_code == "KP" // North Korea
      || client.geo.country_code == "SD" // Sudan
      || client.geo.country_code == "VE" // Venezuela
      || client.geo.region == "UA-43" // Crimea (Region of Ukraine)
      || client.geo.region == "UA-09" // Luhansk (Region of Ukraine)
      || client.geo.region == "UA-14" // Donetsk (Region of Ukraine)
  ) {
    error 405 "Not allowed";
  }
}
