# Introduction

This part of the repo contains Terraform managed AWS infrastructure for CGA-Website.

# Prerequsite

To use or deploy this infrastructure, you will need to have the following tools locally:

- [Terraform](https://www.terraform.io/downloads.html)
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/install-cliv2-mac.html)
- [Kubectl](https://kubernetes.io/docs/tasks/tools/install-kubectl/#install-kubectl-on-macos)

Following the installation of the tool listed above, you would need to setup access key for corresponding accounts.
For the purpose of this subfolder, you would need access to `cga-staging` and optionally to `cga-production`

It is recommended to setup named profiles when setting up these access keys, as the terraform template can automatically switch between the corresponding creidentials based on terraform workspace selected.

Use the name `cga-staging` for the profile that access `cga-staging`.
Use the name `cga-production` for the profile that access `cga-production`.
  

## Deployment

To deploy, simply run the script in the directory, `apply-staging.sh` for deploying to staging environment, `apply-prod.sh` for deploying to production environment.
You would need to provide the keys listed in `.env.example` file befor running the script.

## Testing

- Use curl -ILk www.staging.cga.school to test the staging environment
