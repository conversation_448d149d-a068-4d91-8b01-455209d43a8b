provider "fastly" {
  version = "0.20.1"
}

locals {
  env = terraform.workspace

  names = {
    default = "Crimson Global Academy (test)"
    staging = "Crimson Global Academy (staging)"
    prod    = "Crimson Global Academy (prod)"
  }

  backend = {
    default = "cga-website-staging-preview.s3-website-ap-southeast-2.amazonaws.com"
    staging = "cga-website-staging.s3-website-ap-southeast-2.amazonaws.com"
    prod    = "cga-website-prod.s3-website-ap-southeast-2.amazonaws.com"
  }

  domainsLookup = {
    naked_domains = {
      default = "test.crimsonglobalacademy.school"
      staging = "staging.crimsonglobalacademy.school"
      prod    = "crimsonglobalacademy.school"
    }

    www_domains = {
      default = "www.test.crimsonglobalacademy.school"
      staging = "www.staging.crimsonglobalacademy.school"
      prod    = "www.crimsonglobalacademy.school"
    }

    dotcom_domains = {
      prod = "crimsonglobalacademy.com"
    }

    www_dotcom_domains = {
      prod = "www.crimsonglobalacademy.com"
    }

    cga_school_domains = {
      staging = "staging.cga.school"
      prod    = "cga.school"
    }

    www_cga_school_domains = {
      staging = "www.staging.cga.school"
      prod    = "www.cga.school"
    }

    cga_online_highschool_domains = {
      prod = "crimsononlinehighschool.com"
    }

    www_cga_online_highschool_domains = {
      prod = "www.crimsononlinehighschool.com"
    }
    
    redirect_campus_domains = {
      prod = "campus.cga.school"
    }

    www_redirect_campus_domains = {
      prod = "www.campus.cga.school"
    }
  }

  # At minimal, all domains need to include naked_domains and www_domains
  defaultDomainKeys        = ["naked_domains", "www_domains"]
  additionalStagingDomains = ["cga_school_domains", "www_cga_school_domains"]
  additionalProdDomains = [
    "dotcom_domains", "www_dotcom_domains",
    "cga_school_domains", "www_cga_school_domains",
    "cga_online_highschool_domains", "www_cga_online_highschool_domains", "redirect_campus_domains", "www_redirect_campus_domains"
  ]
  # Each environment can have additional domains besides the default, they can
  # be added to the array after.
  domains = {
    default = concat(local.defaultDomainKeys, [])
    staging = concat(local.defaultDomainKeys, local.additionalStagingDomains)
    prod    = concat(local.defaultDomainKeys, local.additionalProdDomains)
  }

  # Datadog
  # See: https://docs.fastly.com/en/guides/custom-log-formats for log formats
  dd_format_object = {
    "service" : "fastly-cga",
    "env" : local.env,
    "userAgent" : "%%{User-agent}i",
    "referer" : "%%{Referer}i",
    "message" : "%m %U",
    "time" : "%t",
    "remoteIP" : "%a",
    "host" : "%V",
    "request" : "%U",
    "query" : "%q",
    "method" : "%m",
    "status" : "%s"
  }
  # We need the `replace` here because otherwise `jsonencode` would also encodes the `>` character
  dd_format = replace(jsonencode(local.dd_format_object), "%s", "%>s")
}

# Create a new Fastly service called crimsonglobalacademy_school
# Fastly_service_v1 docs: https://www.terraform.io/docs/providers/fastly/r/service_v1.html
resource "fastly_service_v1" "crimsonglobalacademy_school" {
  name = local.names[local.env]

  logging_datadog {
    name   = "logging-datadog-${local.env}"
    token  = var.dd_api_key
    format = local.dd_format
    region = "EU"
  }

  # Dynamically create domains rather than creating each of them separately,
  # See: https://www.hashicorp.com/blog/hashicorp-terraform-0-12-preview-for-and-for-each
  #  and https://www.terraform.io/docs/configuration/resources.html#for_each-multiple-resource-instances-defined-by-a-map-or-set-of-strings
  # for reference
  dynamic "domain" {
    for_each = local.domains[local.env]

    content {
      name = lookup(local.domainsLookup, domain.value, "")[local.env]
    }
  }

  backend {
    address          = local.backend[local.env]
    name             = "cga"
    port             = 80
    use_ssl          = false
    ssl_check_cert   = false
    auto_loadbalance = false
  }

  force_destroy = true

  request_setting {
    name      = "Force SSL"
    force_ssl = true
  }

  gzip {
    name          = "Default Gzip Policy"
    content_types = ["text/html", "application/x-javascript", "text/css", "application/javascript", "text/javascript", "application/json", "application/vnd.ms-fontobject", "application/x-font-opentype", "application/x-font-truetype", "application/x-font-ttf", "application/xml", "font/eot", "font/opentype", "font/otf", "image/svg+xml", "image/vnd.microsoft.icon", "text/plain", "text/xml"]
    extensions    = ["css", "js", "html", "eot", "ico", "otf", "ttf", "json", "svg"]
  }

  vcl {
    name    = "Main VCL file"
    content = file("${path.module}/main.vcl")
    main    = true
  }

  vcl {
    name    = "Utils"
    content = file("${path.module}/../../shared/utils.vcl")
  }

  vcl {
    name    = "Redirects VCL"
    content = file("${path.module}/redirects.vcl")
  }

}
