const { get } = require('./http');

describe('HTTPS Redirection', () => {
  it('should redirect HTTP to HTTPS', async () => {
    const response = await get('http://www.test.crimsonglobalacademy.school');
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsonglobalacademy.school/'
    );
  });
});

describe('Naked domain redirection', () => {
  it('should redirect naked to www domain', async () => {
    const response = await get('https://test.crimsonglobalacademy.school');
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsonglobalacademy.school/'
    );
  });
});

describe('Geolocation code should be injected', () => {
  it('should redirect / to /nz', async () => {
    const response = await get('https://www.test.crimsonglobalacademy.school');
    expect(response.statusCode).toBe(302);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsonglobalacademy.school/nz/'
    );
  });

  it('should redirect dot com domains', async () => {
    const response = await get('https://www.crimsonglobalacademy.com');
    expect(response.statusCode).toBe(301);
    expect(response.headers['location']).toBe(
      'https://www.crimsonglobalacademy.school'
    );
  });

  it('should redirect /abcd to /nz/abcd', async () => {
    const response = await get(
      'https://www.test.crimsonglobalacademy.school/abcd'
    );
    expect(response.statusCode).toBe(302);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsonglobalacademy.school/nz/abcd'
    );
  });

  it('should redirect /qs-crimson to /nz/qs-crimson', async () => {
    const response = await get(
      'https://www.test.crimsonglobalacademy.school/qs-crimson'
    );
    expect(response.statusCode).toBe(302);
    expect(response.headers['location']).toBe(
      'https://www.test.crimsonglobalacademy.school/nz/qs-crimson'
    );
  });

  it('should not inject country code for /nz?x=123', async () => {
    const response = await get(
      'https://www.test.crimsonglobalacademy.school/nz?x=123'
    );
    expect(response.statusCode).toBe(200);
  });
});

describe('No redirections', () => {
  it('/nz should work without redirection', async () => {
    const response = await get(
      'https://www.test.crimsonglobalacademy.school/nz'
    );
    expect(response.statusCode).toBe(200);
  });
});
