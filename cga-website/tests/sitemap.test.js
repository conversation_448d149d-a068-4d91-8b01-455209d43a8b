const { get } = require('./http');

describe('sitemap.xml', () => {
  it('should return 200 for sitemap.xml without any redirection', async () => {
    const response = await get(
      'https://www.test.crimsonglobalacademy.school/sitemap.xml'
    );
    expect(response.statusCode).toBe(200);
  }, 30000); // Set jest async timeout to 30 seconds

  it('robots.txt should return 200 without redirection', async () => {
    const response = await get(
      'https://www.test.crimsonglobalacademy.school/robots.txt'
    );
    expect(response.statusCode).toBe(200);
  });
});
