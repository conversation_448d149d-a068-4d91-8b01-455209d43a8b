terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 2.28.1"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.1"
    }
    kubernetes = {
      source  = "registry.terraform.io/hashicorp/kubernetes"
      version = "~> 2.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 1.2"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 2.1"
    }
  }
  required_version = ">= 0.13"
}

resource "kubernetes_deployment" "gtm-server" {
  metadata {
    name = "${var.is_preview ? "preview" : "live"}-gtm-server"
    namespace = "gtm-server"
    labels = {
      App = "gtm-server"
    }
  }

  spec {
    selector {
      match_labels = {
        App = "gtm-server"
      }
    }

    template {
      metadata {
        labels = {
          App = "gtm-server"
        }
      }

      spec {
        container {
          image = "gcr.io/cloud-tagging-10302018/gtm-cloud-image:stable"
          name  = "gtm-server-host"

          resources {
            limits = {
              cpu    = "100m"
              memory = "64Mi"
            }
          }

          liveness_probe {
            http_get {
              path = "/healthz"
              port = 8080
            }

            initial_delay_seconds = 10
            period_seconds        = 5
          }

          env {
            name  = "CONTAINER_CONFIG"
            value = var.gtm_container_config
          }

          env {
            name  = "RUN_AS_PREVIEW_SERVER"
            value = var.is_preview
          }

          dynamic "env" {
            for_each = var.is_preview ? [] : [1]
            content {
              name  = "PREVIEW_SERVER_URL"
              value = "https://preview.${var.domain_name[0]}"
            }
          }
        }
      }

    }
  }
}

resource "kubernetes_service" "gtm-server" {
  metadata {
    name = "${var.is_preview ? "preview" : "live"}-gtm-server"
  }

  spec {
    selector = { App = "gtm-server" }

    port {
      port        = 80
      target_port = 8080
      protocol    = "TCP"
    }

  }
}

resource "kubernetes_ingress" "gtm-server" {
  metadata {
    name = "${var.is_preview ? "preview" : "live"}-gtm-server"
    annotations = {
      "kubernetes.io/ingress.class" = "nginx"
    }
  }

  spec {
    dynamic "rule" {
      for_each = var.domain_name
      content {
        host = "${var.is_preview ? "preview" : "live"}.${rule.value}"
        http {
          path {
            path = "/"
            backend {
              service_name = kubernetes_service.gtm-server.metadata.0.name
              service_port = 80
            }
          }
        }
      }
    }
  }
}

