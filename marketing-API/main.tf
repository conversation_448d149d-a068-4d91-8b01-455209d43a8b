terraform {
  backend "s3" {}

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 3.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = ">= 2.10"
    }
    kubernetes = {
      source  = "registry.terraform.io/hashicorp/kubernetes"
      version = ">= 2.21"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 1.2"
    }
    # TODO: Try to get rid of this dependency. Unsure which modules is messed up
    template = {
      source  = "hashicorp/template"
      version = "~> 2.2"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 2.1"
    }
  }
  required_version = ">= 0.13"
}

provider "aws" {
  region  = var.aws_region
  profile = var.workspace_aws_profile[terraform.workspace]
}

locals {
  cross_zone_load_balancing_config = {
    production = "true",
    staging    = "false",
  }
  cross_zone_load_balancing_enabled = local.cross_zone_load_balancing_config[terraform.workspace]
  website_hosted_zone_name = "${local.env}.www.crimsoneducation.io"

}

# Kubernetes variables
data "aws_eks_cluster" "cluster" {
  name = module.eks.cluster_id
}

data "aws_eks_cluster_auth" "cluster" {
  name = module.eks.cluster_id
}

# Load Kubernetes Platform config.
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority.0.data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

# Helm is Kubernetes'package manager.
# More information: https://www.terraform.io/docs/providers/helm/index.html
provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority.0.data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

module "eks" {
  # Using this terraform module for easy setup and config:
  # https://registry.terraform.io/modules/terraform-aws-modules/eks/aws
  source  = "terraform-aws-modules/eks/aws"
  version = "17.24.0"

  # Version of Kubernates cluster to use
  # https://docs.aws.amazon.com/eks/latest/userguide/kubernetes-versions.html
  cluster_version = "1.21"

  # Name of the EKS cluster. Also used as a prefix in names of related resources.
  cluster_name = local.cluster_name

  # Modify these to control cluster access
  cluster_endpoint_private_access = true
  cluster_endpoint_public_access  = true

  # We don't need kubeconfig locally
  write_kubeconfig = false

  # Makes specifying IAM users or roles with cluster access easier
  manage_aws_auth = true

  # A list of the desired control plane logging to enable.
  cluster_enabled_log_types = ["api", "audit", "controllerManager", "scheduler"]

  # The cluster primary security group ID created by the EKS cluster on 1.14 or later.
  cluster_security_group_id = aws_security_group.cluster_access_sg.id

  # Private AND public subnets
  subnets = concat(module.public_subnets.subnet_ids, module.private_subnets.subnet_ids)

  tags = {
    "Environment" : local.platform_name
    (local.cluster_tag) : 1
  }

  # VPC where the cluster and workers will be deployed.
  vpc_id = aws_vpc.vpc.id

  # Managed EKS Node Group (V14+)
  # Doc: https://docs.aws.amazon.com/eks/latest/userguide/managed-node-groups.html
  node_groups = {
    main = {
      # AutoScaler Enabled, set to max and autoscaler will downscale appropriately. This ensures that nodes aren't killed when an apply is performed.
      desired_capacity = var.max_nodes
      max_capacity     = var.max_nodes
      min_capacity     = var.min_nodes
      subnets          = module.private_subnets.subnet_ids
      iam_role_arn     = aws_iam_role.eks_node_role.arn
      instance_types   = ["t3a.medium"]
      # Have to manually set the AMI version for nodes
      ami_release_version = "1.21.14-20220811"
    }
  }

  worker_security_group_id = aws_security_group.cluster_access_sg.id
}

# A helm release is an instance of chart running in a Kubernetes cluster. A Chart is a Helm package
# The code below installs cluster-autoscaler in the Kubernetes cluster.
# Helm release doc: https://www.terraform.io/docs/providers/helm/r/release.html
# Helm Cluster-autoscaler Chart doc: https://hub.helm.sh/charts/stable/cluster-autoscaler/7.2.2
# AWS Cluster Autoscaler doc: https://docs.aws.amazon.com/eks/latest/userguide/cluster-autoscaler.html
resource "helm_release" "cluster_autoscaler" {
  name       = "cluster-autoscaler"
  repository = "https://kubernetes.github.io/autoscaler"
  chart      = "cluster-autoscaler"
  namespace  = "kube-system"
  version    = "9.29.1"

  set {
    name  = "autoDiscovery.enabled"
    value = "true"
  }

  set {
    name  = "awsRegion"
    value = var.aws_region
  }

  set {
    name  = "autoDiscovery.clusterName"
    value = module.eks.cluster_id
  }

  set {
    name  = "cloudProvider"
    value = "aws"
  }

  set {
    name  = "rbac.create"
    value = "true"
  }

  set {
    name  = "sslCertPath"
    value = "/etc/ssl/certs/ca-bundle.crt"
  }
}

resource "helm_release" "datadog" {
  name            = "datadog-logging"
  repository      = "https://helm.datadoghq.com"
  chart           = "datadog"
  namespace       = "datadog"
  cleanup_on_fail = true
  version         = "3.32.2"

  create_namespace = "true"

  set_sensitive {
    name  = "datadog.apiKey"
    value = var.dd_api_key
  }

  set {
    name  = "datadog.site"
    value = "datadoghq.eu"
  }
  set {
    name  = "datadog.clusterName"
    value = local.cluster_name
  }
  set {
    name  = "datadog.logs.enabled"
    value = true
  }
  set {
    # We still need this although we've configured include/exclude rules below
    name  = "datadog.logs.containerCollectAll"
    value = true
  }
  set {
    name  = "datadog.containerExcludeLogs"
    value = "image:.*"
  }
  set {
    name  = "datadog.containerIncludeLogs"
    value = "image:.*marketing-website-api.*"
  }
  set {
    name  = "agents.containers.agent.resources.limits.cpu"
    value = "200m"
  }
  set {
    name  = "agents.containers.agent.resources.limits.memory"
    value = "256Mi"
  }
}

# Support multiple services
module "ingress" {
  source     = "./nginxIngress"
  depends_on = [module.eks, aws_acm_certificate_validation.validation_tagging] # Require Node Group Attach for Deployment.

  name          = "marketing-nginx"
  namespace     = "marketing-ingress"
  ingress_class = "nginx"

  config = {
    controller = {
      publishService = {
        enabled = true
      }
      metrics = {
        enabled = true
      }
      service = {
        annotations = {
          "service.beta.kubernetes.io/aws-load-balancer-type"                              = "nlb"
          "service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled" = local.cross_zone_load_balancing_enabled
          "service.beta.kubernetes.io/aws-load-balancer-ssl-cert"                          = aws_acm_certificate.certificate_tagging.arn
          "service.beta.kubernetes.io/aws-load-balancer-ssl-ports"                         = "https"
          "service.beta.kubernetes.io/aws-load-balancer-backend-protocol"                  = "tcp"
        }
        targetPorts = {
          https = "http"
        }
      }
      config = {
        "use-proxy-protocol"    = "true"
        "use-forwarded-headers" = "true"
      }
    }
  }
}

# Get the right ingress_address.
locals {
  ingress_address = module.ingress.ingress_address

  # Expects load balancer name to be in AWS format.
  public_elb_name = split("-", local.ingress_address)[0]

  # Resulting arn of the load balancer.
  public_elb_arn = data.aws_lb.public_elb.arn
}

# Get ELB Zone ID For Alias
data "aws_lb" "public_elb" {
  name = local.public_elb_name
}

// NOTE: THIS IS A WORKAROUND TO FIX NLB TLS TERMINATION
// SEE: https://github.com/kubernetes/kubernetes/issues/57250
// This is because we need to switch to the out of tree AWS Load Balancer controller available with Kubernetes 1.19+
resource "null_resource" "lb_proxy_protocol_v2" {
  triggers = {
    lb_name = local.public_elb_name
  }

  provisioner "local-exec" {
    command = "node ${path.module}/proxyProtocolV2.js"
    environment = {
      lb_arn     = local.public_elb_arn
      AWS_REGION = var.aws_region
    }
  }
}

locals {
  primary_zone_name = "${terraform.workspace}.web.crimsoneducation.org"
  zone_name = toset([
    local.primary_zone_name,
    "${terraform.workspace}.web.crimsonglobalacademy.school",
    "${terraform.workspace}.web.medvieweducation.org"
  ])
}

data "aws_route53_zone" "marketing_zone" {
  for_each = local.zone_name
  name     = each.value
}

resource "aws_route53_record" "tagging" {
  for_each = local.zone_name
  zone_id  = data.aws_route53_zone.marketing_zone[each.key].zone_id
  name     = "*.tagging"
  type     = "A"

  alias {
    name                   = data.aws_lb.public_elb.dns_name
    zone_id                = data.aws_lb.public_elb.zone_id
    evaluate_target_health = false
  }
}

# Generate an App Certificate.
resource "aws_acm_certificate" "certificate_tagging" {
  domain_name               = "*.tagging.${local.primary_zone_name}"
  subject_alternative_names = [for s in local.zone_name : "*.tagging.${s}" if s != local.primary_zone_name]
  validation_method         = "DNS"

  tags = {
    Name = "Website Server-side Tagging SSL Cert - ${terraform.workspace}"
  }

  lifecycle {
    create_before_destroy = true
  }
}


# Create Certificate Validation Record.
resource "aws_route53_record" "validation_record_tagging" {
  for_each = {
    for dvo in aws_acm_certificate.certificate_tagging.domain_validation_options : dvo.domain_name => {
      domain_name = dvo.domain_name
      name        = dvo.resource_record_name
      record      = dvo.resource_record_value
      type        = dvo.resource_record_type
    }
  }
  zone_id         = data.aws_route53_zone.marketing_zone[trimprefix(each.value.domain_name, "*.tagging.")].zone_id
  name            = each.value.name
  type            = each.value.type
  records         = [each.value.record]
  ttl             = 60
  allow_overwrite = true
}

resource "aws_acm_certificate_validation" "validation_tagging" {
  certificate_arn         = aws_acm_certificate.certificate_tagging.arn
  validation_record_fqdns = [for record in aws_route53_record.validation_record_tagging : record.fqdn]
}

module "preview-gtm-server" {
  count = local.env == "staging" ? 1 : 0
  source     = "../gtm-server"
  depends_on = [module.ingress]

  is_preview  = true
  domain_name = [for zone in local.zone_name : "tagging.${zone}"]
}

module "live-gtm-server" {
  source     = "../gtm-server"
  depends_on = [module.ingress]

  is_preview  = false
  domain_name = [for zone in local.zone_name : "tagging.${zone}"]
}

data "aws_route53_zone" "zone" {
  name = local.website_hosted_zone_name
}

module "network" {
  source = "./modules/network"

  namespace = local.env
  region    = var.aws_region

  vpc_id = aws_vpc.vpc.id
  private_subnets = module.private_subnets.subnet_ids
  public_subnets = module.public_subnets.subnet_ids

  hosted_zone_id = data.aws_route53_zone.zone.zone_id
}

