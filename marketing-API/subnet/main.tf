# Module Dependency Hack: https://github.com/hashicorp/terraform/issues/10462, https://github.com/hashicorp/terraform/issues/1178
variable "dependencies" {
  type    = list(any)
  default = []
}
resource "null_resource" "dependency_getter" {
  count = length(var.dependencies) > 0 ? 1 : 0

  triggers = {
    dependencies = length(var.dependencies)
  }

  provisioner "local-exec" {
    command = "echo 'Waited for ${length(var.dependencies)} dependencies'"
  }
}

data "aws_availability_zones" "available" {}

locals {
  prefix            = "${var.environment}_${var.subnet_type}"
  subnet_count      = length(var.cidr_blocks)
  route_table_count = var.public ? 1 : local.subnet_count
  tags = merge({
    Environment = var.environment
    Network     = var.public ? "Public" : "Private"
  }, var.subnet_tags)
}

# Routing
resource "aws_route_table" "route_table" {
  count = var.enable ? local.route_table_count : 0

  vpc_id = var.vpc_id
  tags = merge({
    Name = "${local.prefix}_rt_${count.index}"
  }, local.tags)
}

# Subnet
resource "aws_subnet" "subnet" {
  count = var.enable ? local.subnet_count : 0

  vpc_id            = var.vpc_id
  availability_zone = var.multi_az ? data.aws_availability_zones.available.names[count.index] : data.aws_availability_zones.available.names[0]
  cidr_block        = var.cidr_blocks[count.index]
  map_public_ip_on_launch = var.public ? true : false

  tags = merge({
    Name = "${local.prefix}_subnet_${count.index}"
  }, local.tags)
}

# Associate Subnet with Route Table.
resource "aws_route_table_association" "route_table_association" {
  count = var.enable ? local.subnet_count : 0

  subnet_id      = aws_subnet.subnet[count.index].id
  route_table_id = aws_route_table.route_table[var.public ? 0 : count.index].id
}

# Internet Route if Public.
resource "aws_route" "internet_gateway_route" {
  count = (var.enable && var.public) ? 1 : 0

  route_table_id         = aws_route_table.route_table.0.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = var.internet_gateway
}

# NAT Gateway if Private.
resource "aws_eip" "nat_eip" {
  count = (var.enable && ! var.public) ? local.subnet_count : 0
  vpc   = true
}
resource "aws_nat_gateway" "nat" {
  count         = (var.enable && ! var.public) ? local.subnet_count : 0
  allocation_id = aws_eip.nat_eip[count.index].id
  subnet_id     = var.public_subnets[count.index] # Provide Public Subnet Ids.

  tags = {
    Name = "${local.prefix}_nat_${count.index}"
  }
}

# NAT Route if Private.
resource "aws_route" "nat_gateway_route" {
  count = (var.enable && ! var.public) ? local.subnet_count : 0

  route_table_id         = aws_route_table.route_table[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat[count.index].id
}
