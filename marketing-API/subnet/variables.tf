variable "enable" {
  type    = bool
  default = true
}


variable "vpc_id" {
  type = string
}

variable "environment" {
  type = string
}

variable "subnet_type" {
  type = string
}

variable "subnet_tags" {
  type    = map(string)
  default = {}
}

variable "public" {
  type    = bool
  default = false
}

variable "internet_gateway" {
  type    = string
  default = ""
}

variable "public_subnets" {
  description = "If Private, list of public subnets to connect with NAT"
  type        = list(string)
  default     = []
}

variable "cidr_blocks" {
  description = "List of CIDRs, length determines how many subnets to create."
  type        = list(string)
}

variable "multi_az" {
  type    = bool
  default = true
}
