# Accessing the Database

To access the database for Website, you need to connect via SSH Tunnel.

## Connecting to the SSH Tunnel

To connect to the SSH Tunnel, you must first generate an SSH Key pair on you machine if you don't have one, then upload it to the SSH Tunnel S3 Bucket, so that you are authorized to access the SSH Tunnel, then you can use either your Database Client if it supports specifying SSH Configuration directly, or an SSH Tool like <PERSON>ty or the built-in SSH Client, to connect to the SSH Tunnel.

1. Open a Command Line in your user profile and execute: `ssh-keygen -t rsa` (This tool exists on all OSes, if using Windows 10).
2. It will ask for a file name to save the key, you could use `.ssh/{{username}}`, where `{{username}}` is the name you want to log in to the SSH Tunnel with (This should be recognisable that is yours by the rest of the team, or we will delete it). E.g. `.ssh/victortsyan`.
   **Note this name must not have any underscores, spaces, or dashes.**
3. It will then ask for a passphrase, Just keep pressing enter to accept the defaults until the program exits.
4. Note down the paths for the public key and private keys, we will need these to access the tunnel.
5. Open the AWS Management Console, Assume into the Marketing AWS Subaccount for the environment, e.g. `marketing-staging`.
6. Go to S3, and find the `website-{{environment}}-ssh-tunnel` bucket, e.g. `website-staging-ssh-tunnel`, this is where we will copy the public key from ssh-keygen.
7. Open the bucket, and go to the `public-keys/` subfolder.
8. Copy your public key (`.pub`) file generated above, this will tell the SSH Tunnel that you are authorized to access it, and it will generate a user for you, (using the filename without .pub as the username).
9. Give it a minute or two to pickup your file, and you should then be able to access the tunnel.
10. You can connect and test if the ssh tunnel is working by executing `ssh -i .ssh/{{username}} {{username}}@{{tunnel_address}}`, where `{{username}}` is what you set above, and `{{tunnel_address}}` is specified below.

### Tunnel Addresses

| Environment | Address                                  |
| ----------- | ---------------------------------------- |
| Staging     | `tunnel.staging.www.crimsoneducation.io` |
| Production  | `#TODO: Update this when going to prod`  |

## Database Credentials

Once you are connected to the SSH Tunnel, you can now get the Database Credentials from Secrets Manager (Assume into the Marketing AWS Subaccount for the environment, e.g. `marketing-staging`), go to secrets manager, and then open the secret: `website/{{environment}}/{{service-name}}/db-config`. The rds address will be accessible when you have the ssh tunnel open.

### Postico SSH Configuration

1. If you have an existing favourite, click edit, then use the options dropdown, if you are creating a new favourite, select the options dropdown.
2. Click connect with SSH, this will expand more input boxes to fill out.
3. In the SSH Host, put the tunnel address above, the ssh port is 22.
4. The username below the SSH Host is your ssh username, e.g. `victortsyan`.
5. The password is blank.
6. Use the private key picker to pick your ssh private key from before.
7. Click Save/Done, you should now be able connect to the Database.

### DBeaver SSH Configuration

1. If you have an existing connection, right click it and select edit connection, otherwise create a new connection, and select Postgresql.
2. Switch to the SSH Tab.
3. Click the checbox: "Use SSH Tunnel".
4. Set the Host/IP to the tunnel address.
5. Set the User Name to your ssh username, e.g. `victortsyan`.
6. Set the Authentication Method to Public Key.
7. Use the Private Key file picker to pick your private key.
8. Click "Test tunnel configuration" to ensure it works.
9. Press OK to save your connection.

### WebStorm Data Source and SSH config

1. Create a Postgres Data Source in the Database panel.
2. Click the SSH/SSL tab and check "Use SSH tunnel".
3. Click the dots button next to the drop-down to create a new SSH tunnel config.
4. Fill in the fields in this dialog, host is either "#TODO: Update this when going to prod" (prod) or "tunnel.staging.www.crimsoneducation.io" (staging).
   User name is your private key file/username selected in step 2 of the main guide (e.g. "victorcrimson").
   Authentication type is "Key pair".
   Private key file is the file from step 2 in the main guide.
5. Click test connection, if everything is configured correctly it should connect.
6. In the main config set the fields as per the credentials from AWS Secrets Manager.
7. You most likely want to set Database to "website" to avoid the step of choosing the database each time you connect.
8. It's probably a good idea to also check "Read-only" in the Options tab, and to set a color.
   Setting a color is done by right-clicking the Data Source and clicking "Color settings" in the context menu.
