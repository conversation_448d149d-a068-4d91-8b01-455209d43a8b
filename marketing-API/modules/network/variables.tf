variable "region" {
  type = string
}

variable "namespace" {
  type = string
}

variable "hosted_zone_id" {
  # Hosted zone to expose SSH tunnel on
  type = string
}

variable "vpc_id" {
  type = string
}

variable "public_subnets" {
  description = "If Private, list of public subnets to connect with NAT"
  type        = list(string)
  default     = []
}

variable "private_subnets" {
  type        = list(string)
  default     = []
}

