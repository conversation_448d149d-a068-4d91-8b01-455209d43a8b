locals {
  env = var.namespace
}

module "ssh_tunnel" {
  source      = "github.com/crimson-education/crimson-devops-modules//modules/aws/sshTunnel?ref=cdm-5.0.5"
  region      = var.region
  bucket_name = "website-${local.env}-ssh-tunnel"

  vpc_id          = var.vpc_id
  private_subnets = var.private_subnets
  traffic_subnets = var.public_subnets

  create_dns_record   = true
  hosted_zone_id      = var.hosted_zone_id
  bastion_record_name = "tunnel"
}