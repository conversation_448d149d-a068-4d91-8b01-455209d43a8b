locals {
  env                 = terraform.workspace
  platform_name       = "marketing-api-${local.env}"
  vpc_id              = aws_vpc.vpc.id
  cluster_name        = "${local.platform_name}-cluster"
  cluster_tag         = "kubernetes.io/cluster/${local.cluster_name}"
  vpc_cidr            = var.vpc_cidr
  public_cidr         = cidrsubnet(local.vpc_cidr, 1, 0) # ***********/17
  private_cidr        = cidrsubnet(local.vpc_cidr, 1, 1) # *************/17
  internet_gateway_id = aws_internet_gateway.igw.id
}

variable "vpc_cidr" {
  type    = string
  default = "***********/16"
}

# Create the VPC.
resource "aws_vpc" "vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_support   = true
  enable_dns_hostnames = true
  tags = {
    Name = "${local.platform_name}-vpc",
    // EKS Assignment.
    (local.cluster_tag) = "shared"
  }
}

# Create the Internet Gateway.
resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.vpc.id
  tags = {
    Name = "${local.platform_name}-igw"
  }
}

module "public_subnets" {
  source = "./subnet"

  vpc_id      = local.vpc_id
  subnet_type = "public"
  cidr_blocks = [
    cidrsubnet(local.public_cidr, 7, 0), # ***********/24
    cidrsubnet(local.public_cidr, 7, 1), # ***********/24
    cidrsubnet(local.public_cidr, 7, 2), # ***********/24
  ]
  public           = true
  internet_gateway = local.internet_gateway_id
  environment      = local.platform_name

  subnet_tags = {
    (local.cluster_tag)   = "shared"
    "kubernetes.io/role/elb" = "1"
  }
}

module "private_subnets" {
  source       = "./subnet"
  dependencies = [module.public_subnets]

  vpc_id      = local.vpc_id
  subnet_type = "private"
  cidr_blocks = [
    cidrsubnet(local.private_cidr, 3, 0), # *************/20
    cidrsubnet(local.private_cidr, 3, 1), # 192.168.144.0/20
    cidrsubnet(local.private_cidr, 3, 2), # 192.168.176.0/20
  ]
  public         = false
  public_subnets = module.public_subnets.subnet_ids

  environment = local.platform_name

  subnet_tags = {
    (local.cluster_tag)            = "shared"
    "kubernetes.io/role/internal-elb" = "1"
  }
}
