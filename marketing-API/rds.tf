resource "random_password" "db_password" {
  length  = 32
  special = false
}

locals {
  db_username = "website_user"
  db_password = random_password.db_password.result

  db_config = sensitive({
    DB_HOST            = module.website_database.root_address
    DB_USER            = local.db_username
    DB_PASSWORD        = local.db_password
    DB_PORT            = "5432"
    DB_SSL             = "require"
    DB_MANAGEMENT_NAME = "postgres"
  })
}


module "website_database" {
  source = "github.com/crimson-education/crimson-devops-modules//modules/aws/managedDatabase?ref=cdm-5.2.6"

  vpc_id      = aws_vpc.vpc.id
  use_subnets = module.private_subnets.subnet_ids
  cidr_blocks = module.private_subnets.subnet_cidrs

  engine          = "postgres"
  engine_version  = "15.5"
  environment     = local.env
  storage_size_gb = 20
  storage_type    = "gp2"

  db_instance_type = "db.t4g.small"

  name          = "website-db"
  root_username = local.db_username
  root_password = local.db_password

  storage_encrypted = true
  protect           = true

  // Only uncomment when necessary:
  # allow_major_upgrade = true
  # apply_immediately   = true
}

resource "aws_secretsmanager_secret" "db_config" {
  name = "website/${local.env}/db-config"
}

resource "aws_secretsmanager_secret_version" "db_config" {
  secret_id     = aws_secretsmanager_secret.db_config.id
  secret_string = jsonencode(local.db_config)
}
