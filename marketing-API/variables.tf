variable "workspace_aws_profile" {
  default = {
    staging    = "marketing-staging"
    production = "marketing-production"
  }
}

variable "dd_api_key" {
  type        = string
  description = "Datadog logging"
}

variable "aws_region" {
  type        = string
  description = "Used AWS Region. Default ap-southeast-2 is Sydney"
  default     = "ap-southeast-2"
}

# EKS specific variables
variable "instance_type" {
  type        = string
  description = "Name of the type of instance to use, defualt to t2.medium"
  default     = "t3.small"
}

variable "min_nodes" {
  type        = number
  description = "Minimum number of workers in managed node group in EKS cluster"
  default     = 2
}

variable "max_nodes" {
  type        = string
  description = "Maximum number of workers in managed node group in EKS cluster"
  default     = 3
}
