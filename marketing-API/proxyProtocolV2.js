// NOTE: THIS IS A WORKAROUND TO FIX NLB TLS TERMINATION
// SEE: https://github.com/kubernetes/ingress-nginx/issues/5051
const { execSync } = require("child_process");
const {
  lb_arn,
} = process.env;

function validationError(msg, status = -1) {
  console.log(msg);
  process.exit(status);
}

function execForResult(command) {
  return execCommand(command, { log: false });
}

function execCommand(command, { log = true, env = {} } = {}) {
  try {
    return execSync(command, {
      encoding: 'utf-8',
      stdio: log ? 'inherit' : 'pipe',
      env: {
        ...process.env,
        ...env,
      }
    });
  } catch (e) {
    validationError(e.message, e.status);
  }
}

console.log(`Setting Proxy Protocol V2 for NLB: ${lb_arn}`);
const listeners = JSON.parse(execForResult(`aws elbv2 describe-listeners --load-balancer-arn ${lb_arn} --query Listeners --output json`));

// Only apply to 80 and 443.
const ListenersToProxy = listeners
  .filter(listener => [80, 443].includes(listener.Port));

for (const listener of ListenersToProxy) {
  const targetGroupArn = listener.DefaultActions[0].TargetGroupArn;
  console.log(`Setting Proxy Protocol on Target Group for Port ${listener.Port}: ${targetGroupArn}`);
  execCommand(`aws elbv2 modify-target-group-attributes --target-group-arn ${targetGroupArn} --attributes Key=proxy_protocol_v2.enabled,Value=true`);
}
