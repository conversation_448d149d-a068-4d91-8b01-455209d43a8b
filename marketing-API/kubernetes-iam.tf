####################
# CLUSTER SECURITY #
####################

data "aws_iam_policy_document" "cluster_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["eks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "eks_cluster_role" {
  name               = "${local.platform_name}-cluster-role"
  assume_role_policy = data.aws_iam_policy_document.cluster_role_policy.json
}

resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSClusterPolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.eks_cluster_role.name
}

resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSServicePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSServicePolicy"
  role       = aws_iam_role.eks_cluster_role.name
}

# Cluster Access Security Group for Connecting Master Nodes and Worker Nodes.
resource "aws_security_group" "cluster_access_sg" {
  name        = "${local.platform_name}-cluster-access-sg"
  description = "Cluster Access Security Group"
  vpc_id      = aws_vpc.vpc.id

  tags = {
    Name = "devops_eks_cluster_access_sg"
  }
}

####################
#   NODE SECURITY  #
####################

# Node Base Role.
data "aws_iam_policy_document" "node_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}
resource "aws_iam_role" "eks_node_role" {
  name               = "${local.platform_name}-eks-node-role"
  assume_role_policy = data.aws_iam_policy_document.node_role_policy.json
}

# Autoscaling policy.
data "aws_iam_policy_document" "node_autoscale_policy" {
  statement {
    actions = [
      "autoscaling:DescribeAutoScalingGroups",
      "autoscaling:DescribeAutoScalingInstances",
      "autoscaling:DescribeLaunchConfigurations",
      "autoscaling:DescribeTags",
      "autoscaling:SetDesiredCapacity",
      "autoscaling:TerminateInstanceInAutoScalingGroup",
      "ec2:DescribeLaunchTemplateVersions"
    ]
    resources = ["*"]
    effect    = "Allow"
  }
}
resource "aws_iam_policy" "EKS_Autoscale_Policy" {
  name   = "EKS_Autoscale_Policy"
  path   = "/"
  policy = data.aws_iam_policy_document.node_autoscale_policy.json
}
resource "aws_iam_role_policy_attachment" "node_EKS_Autoscale_Policy" {
  policy_arn = aws_iam_policy.EKS_Autoscale_Policy.arn
  role       = aws_iam_role.eks_node_role.name
}

# EKS Policies.
resource "aws_iam_role_policy_attachment" "node_AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.eks_node_role.name
}
resource "aws_iam_role_policy_attachment" "node_AmazonEKS_CNI_Policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.eks_node_role.name
}
resource "aws_iam_role_policy_attachment" "node_AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.eks_node_role.name
}

# Policy to allow access to devops/pipeline secrets.
data "aws_iam_policy_document" "pipeline_secrets_access" {
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetRandomPassword",
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
      "secretsmanager:ListSecrets",
      "secretsmanager:ListSecretVersionIds"
    ]
    resources = ["*"]

    condition {
      test     = "StringEquals"
      variable = "secretsmanager:ResourceTag/access_key"

      values = [
        "devops/pipeline"
      ]
    }
  }
}

resource "aws_iam_policy" "pipeline_secrets_access" {
  name   = "${local.platform_name}-eks-access-pipeline-secrets"
  path   = "/"
  policy = data.aws_iam_policy_document.pipeline_secrets_access.json
}
resource "aws_iam_role_policy_attachment" "pipeline_secrets_access" {
  policy_arn = aws_iam_policy.pipeline_secrets_access.arn
  role       = aws_iam_role.eks_node_role.name
}
