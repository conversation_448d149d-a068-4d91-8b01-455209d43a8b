resource "aws_dynamodb_table" "diagnostic-dynamodb-table" {
  name         = "CGA_ONLINE_DIAGNOSTIC_TEST"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "test_uuid"
  range_key    = "test_submitted_at"

  attribute {
    name = "test_uuid"
    type = "S"
  }

  attribute {
    name = "test_submitted_at"
    type = "S"
  }

  attribute {
    name = "student_email"
    type = "S"
  }

  attribute {
    name = "test_version"
    type = "N"
  }

  global_secondary_index {
    name               = "student-email_test-version"
    hash_key           = "student_email"
    range_key          = "test_version"
    projection_type    = "INCLUDE"
    non_key_attributes = ["test_version"]
  }
}

resource "aws_dynamodb_table" "diagnostic-answer-dynamodb-table" {
  name         = "CGA_ONLINE_DIAGNOSTIC_ANSWER"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "test_level"
  range_key    = "test_version"

  attribute {
    name = "test_level"
    type = "S"
  }

  attribute {
    name = "test_version"
    type = "N"
  }
}

resource "aws_dynamodb_table" "diagnostics-asnwers-dynamodb-table" {
  name         = "CGA_ONLINE_DIAGNOSTICS_ANSWERS"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "version_uuid"

  attribute {
    name = "version_uuid"
    type = "S"
  }

  attribute {
    name = "test_subjectLevel"
    type = "S"
  }

  attribute {
    name = "test_version"
    type = "N"
  }

  global_secondary_index {
    name               = "test-subject-level"
    hash_key           = "test_subjectLevel"
    range_key          = "test_version"
    projection_type    = "INCLUDE"
    non_key_attributes = ["test_subjectLevel"]
  }
}

resource "aws_dynamodb_table" "diagnostics-browser-data-table" {
  name         = "CGA_BROWSER_DATA"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "test_uuid"

  attribute {
    name = "test_uuid"
    type = "S"
  }

  attribute {
    name = "student_email"
    type = "S"
  }

  global_secondary_index {
    name            = "test_uuid_email"
    hash_key        = "student_email"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "diagnostics-questions-dynamodb-table" {
  name         = "CGA_ONLINE_DIAGNOSTICS_QUESTIONS"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "version_uuid"
  range_key    = "created_at"

  attribute {
    name = "version_uuid"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  attribute {
    name = "test_subjectLevel"
    type = "S"
  }

  attribute {
    name = "test_version"
    type = "N"
  }

  attribute {
    name = "isLatest"
    type = "N"
  }

  global_secondary_index {
    name               = "test-subject-level"
    hash_key           = "test_subjectLevel"
    range_key          = "test_version"
    projection_type    = "INCLUDE"
    non_key_attributes = ["test_subjectLevel"]
  }


  global_secondary_index {
    name            = "test-subject-latest"
    hash_key        = "isLatest"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "cga-quiz-answers-dynamodb-table" {
  name         = "CGA_QUIZ_ANSWERS"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "version_uuid"

  attribute {
    name = "version_uuid"
    type = "S"
  }

  attribute {
    name = "test_subjectLevel"
    type = "S"
  }

  attribute {
    name = "test_version"
    type = "N"
  }

  global_secondary_index {
    name               = "test-subject-level"
    hash_key           = "test_subjectLevel"
    range_key          = "test_version"
    projection_type    = "INCLUDE"
    non_key_attributes = ["test_subjectLevel"]
  }
}

resource "aws_dynamodb_table" "cga-quiz-questions-dynamodb-table" {
  name         = "CGA_QUIZ_QUESTIONS"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "version_uuid"
  range_key    = "created_at"

  attribute {
    name = "version_uuid"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  attribute {
    name = "test_subjectLevel"
    type = "S"
  }

  attribute {
    name = "test_version"
    type = "N"
  }

  attribute {
    name = "isLatest"
    type = "N"
  }

  global_secondary_index {
    name               = "test-subject-level"
    hash_key           = "test_subjectLevel"
    range_key          = "test_version"
    projection_type    = "INCLUDE"
    non_key_attributes = ["test_subjectLevel"]
  }


  global_secondary_index {
    name            = "test-subject-latest"
    hash_key        = "isLatest"
    projection_type = "ALL"
  }
}
