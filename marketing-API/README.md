# Introduction

This part of the repo contains Terraform managed AWS infrastructure for Crimson Marketing API.

Technologies involved include: Terraform, Kubernetes, Elastic Kubernetes Service, HELM, AWS/EKS, Docker

# Prerequsite

To use or deploy this infrastructure, you will need to have the following tools locally:

- [Terraform](https://www.terraform.io/downloads.html)
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/install-cliv2-mac.html)
- [Kubectl](https://kubernetes.io/docs/tasks/tools/install-kubectl/#install-kubectl-on-macos)

- tfenv: `brew install tfenv`
  - `arch -x86_64 tfenv install ${version}`

Following the installation of the tool listed above, you would need to setup access key for corresponding accounts.
For the purpose of this subfolder, you would need access to `crimson-marketing-staging` and optionally to `crimson-marketing-production`

You must setup named profiles when setting up these access keys, as the terraform template can automatically switch between the corresponding creidentials based on terraform workspace selected.

Use the name `marketing-staging` for the profile that access `crimson-marketing-staging`.  
Use the name `marketing-production` for the profile that access `crimson-marketing-production`.
Look in 1password for `AWS-Profile-Marketing-{environment}` for the access key and secret key.

Get .env from `Devops Marketing-API env` file in 1password.

## Workspaces and state

- The (state)[https://www.terraform.io/docs/state/index.html] in terraform refer to a mapping between the real infrasture and the local terraform template file
- Each Terraform configuration has an associated backend that defines how operations
  are executed and where persistent data such as the Terraform state are stored.
- The persistent data stored in the backend belongs to a workspace.
- There are 2 different workspaces in this subfolder:
  - staging
  - prod

You will also need relevant permissions in order to deploy.

The state of this infrastructure is stored remotely in S3 backend. Because of the limitation of terraform at the momenet (only support one backend for all workspace), the backend config are stored in separate files for staging and production, they are called `backend_config_production` and `backend_config_staging` respectively. Each time before the config is applied, the script will switch the backend config.

## Folder Structrue

Terraform files are usually organized in modules, there's no strict concept of entry file in terraform, but easier understanding, the main.tf file can be take
as an entry point for this folder.

- main.tf: this is where the main part of the EKS cluster is configured, it contains relevant configuration for EKS and Kubernetes,
  there's also a Helm configuration which is used to install pacakges onto kubernetes.
- ecr.tf: this is the configuration for the ECR also known as where the docker image is stored.
- kubernetes-iam.tf: this is where the necessary permission (IAM) in AWS is set up for kubernetes cluster and modules related to it.
- vpc.tf: this is the configuration for VPC in EKS, it uses the module from subnet folder which is from crimson-core-architecture repo.
- dynamodb.tf: this is the configuration for dynamodb for the CGA diagnostic test. -- Will propbably migrate to CGA subaccount in the future.

## Input

Generally, you don't need to worry about input as the default value has been set in variables.tf file.
But if necessary, you can override variables to suit your need by creating a terraform.tfvar file.
Use the file you created by running `terraform apply --var-file=terraform.tfvar`

## Deployment

To deploy, simply run the script in the directory, `apply-staging.sh` for deploying to staging environment, `apply-prod.sh` for deploying to production environment.
You would need to provide the keys listed in `.env.example` file befor running the script.

## Reference and readings

- Terraform Providers/Modules used in this repo:

  - [terraform-aws-eks](https://registry.terraform.io/modules/terraform-aws-modules/eks/aws/11.1.0)
  - [Kubernetes](https://www.terraform.io/docs/providers/kubernetes/index.html)
  - [Helm](https://www.terraform.io/docs/providers/helm/index.html)
  - [VPC](https://www.terraform.io/docs/providers/aws/r/vpc.html)
  - [Subnet](https://github.com/crimson-education/crimson-devops-modules/tree/master/modules/aws/subnet)

- Further readings on EKS and Kubernetes:
  - [AWS EKS official Guide](https://docs.aws.amazon.com/eks/latest/userguide/getting-started-console.html)
  - [Comprehensive tutorial on Kubernetes and EKS](https://gruntwork.io/guides/kubernetes/how-to-deploy-production-grade-kubernetes-cluster-aws) <= Recommended
