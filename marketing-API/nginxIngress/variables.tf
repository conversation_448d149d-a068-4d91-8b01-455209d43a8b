variable "controller_image" {
  type    = string
  default = "k8s.gcr.io/ingress-nginx/controller"
}

variable "controller_replicas" {
  type    = number
  default = 2
}

variable "chart_version" {
  type    = string
  default = null
}

variable "namespace" {
  type    = string
  default = "ingress-nginx"
}

variable "name" {
  type        = string
  default     = null
  description = "The name of this service. If null, we use the namespace instead."
}

variable "ingress_class" {
  type    = string
  default = "nginx"
}

variable "autoscale" {
  type        = bool
  description = "Autoscale the Controllers?"
  default     = false
}

variable "min_replicas" {
  type    = number
  default = 1
}

variable "max_replicas" {
  type    = number
  default = 2
}

variable "targetCPUUtilizationPercentage" {
  type    = number
  default = 50
}

variable "targetMemoryUtilizationPercentage" {
  type    = number
  default = 50
}

variable "config" {
  type    = any
  default = {}
}

variable "loadbalancer_type" {
  description = "Type of Load Balancing, options are Internal, ExternalName, ExternalIP"
  type        = string
  default     = "Internal"
}

