locals {
  has_ingress = length(data.kubernetes_service.ingress) > 0
}

output "full_name" {
  value = local.name
}

output "ingress_service_name" {
  value = local.ingress_name
}

output "ingress_address" {
  value = local.has_ingress ? data.kubernetes_service.ingress.0.status.0.load_balancer.0.ingress.0.hostname : null
}

output "ingress_ip" {
  value = local.has_ingress ? data.kubernetes_service.ingress.0.status.0.load_balancer.0.ingress.0.ip : null
}

