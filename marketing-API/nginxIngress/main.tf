terraform {
  required_version = "~> 1.1"
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.1"
    }
  }
}

locals {
  version      = var.chart_version != null ? var.chart_version : "3.10.1"
  namespace    = var.namespace
  name         = var.name != null ? var.name : var.namespace
  ingress_name = "${local.name}-ingress-nginx-controller"
}

resource "kubernetes_namespace" "namespace" {
  metadata {
    name = local.namespace
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "kubernetes_config_map" "tcp_services" {
  depends_on = [kubernetes_namespace.namespace]

  metadata {
    name      = "tcp-services"
    namespace = local.namespace
  }

  # We don't want to remove data from this on apply.
  lifecycle {
    ignore_changes = [data]
  }
}

resource "helm_release" "nginxIngress" {
  depends_on = [kubernetes_namespace.namespace, kubernetes_config_map.tcp_services]

  name            = local.name
  namespace       = local.namespace
  chart           = "ingress-nginx"
  repository      = "https://kubernetes.github.io/ingress-nginx"
  version         = var.chart_version
  cleanup_on_fail = true

  # 10 Minute timeout
  timeout = 600

  values = [
    yamlencode({
      rbac = {
        create = true
      }
      defaultBackend = {
        enable = false
      }
      autoscaling = {
        enable                            = var.autoscale
        minReplicas                       = var.min_replicas
        maxReplicas                       = var.max_replicas
        targetCPUUtilizationPercentage    = var.targetCPUUtilizationPercentage
        targetMemoryUtilizationPercentage = var.targetMemoryUtilizationPercentage
      }
      controller = {
        replicaCount = var.controller_replicas
        image = {
          repository = var.controller_image
        }
        config = {
          # Add Forwarding Headers.
          "use-forwarded-headers" = "true"

          # Prevent HTTP: 413
          "proxy-body-size" = "10m"

          # Increase Proxy Buffer size
          # Fixes: upstream sent too big header while reading response header from upstream error in nginx-ingress-controller
          # https://andrewlock.net/fixing-nginx-upstream-sent-too-big-header-error-when-running-an-ingress-controller-in-kubernetes/
          "proxy-buffer-size" = "128k"
        }
        extraArgs = {
          "tcp-services-configmap" = "${local.namespace}/tcp-services"
          "ingress-class"          = var.ingress_class
        }
      }
    }),
    yamlencode(var.config),
  ]
}

data "kubernetes_service" "ingress" {
  count      = var.loadbalancer_type == "Internal" ? 1 : 0
  depends_on = [helm_release.nginxIngress]

  metadata {
    name      = local.ingress_name
    namespace = local.namespace
  }
}

